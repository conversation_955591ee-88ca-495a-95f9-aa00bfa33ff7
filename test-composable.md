# Test useEditProfile Composable

## ✅ Đã tách thành công:

### 1. **Composable Structure** (`src/composables/useEditProfile.ts`)

#### **Exports:**
```typescript
export function useEditProfile() {
  return {
    // Data
    formData,           // ProfileFormData ref
    isSubmitting,       // boolean ref
    isLoading,          // boolean ref
    
    // Options
    genderOptions,      // computed options
    educationOptions,   // computed options  
    maritalStatusOptions, // computed options
    
    // Methods
    loadUserData,       // Load từ API
    loadScannedData,    // Load từ localStorage
    initializeForm,     // Initialize toàn bộ form
    handleSubmit,       // Submit form với validation
    formatDateForAPI,   // Convert timestamp → YYYY-MM-DD
    parseDate,          // Parse date string → timestamp
  };
}
```

#### **ProfileFormData Interface:**
- ✅ Personal Information (12 fields)
- ✅ Contact Information (3 fields)  
- ✅ Work Information (7 fields)
- ✅ Identity Information (5 fields)
- ✅ Banking Information (3 fields)
- **Total: 30 fields**

### 2. **EditProfileView.vue** (Simplified)

#### **Before (383 lines):**
- Tất cả logic trong component
- Duplicate code
- Hard to maintain

#### **After (~80 lines):**
```vue
<script setup>
// Import composable
const {
  formData,
  isSubmitting,
  isLoading,
  genderOptions,
  educationOptions,
  maritalStatusOptions,
  initializeForm,
  handleSubmit,
} = useEditProfile();

// Initialize on mount
onMounted(async () => {
  await initializeForm();
});

// Header action with loading state
onMounted(() => {
  headerActionsStore.set(() =>
    h('button', {
      onClick: handleSubmit,
      disabled: isSubmitting.value,
    }, isSubmitting.value ? t('saving') : t('save'))
  );
});
</script>
```

### 3. **Key Features**

#### **Data Loading:**
- ✅ `initializeForm()`: getCurrentUser → loadUserData → loadScannedData
- ✅ Auto-load user data từ API
- ✅ Auto-load scan data từ localStorage (nếu có)
- ✅ Error handling với toast notifications

#### **Form Submission:**
- ✅ `handleSubmit()`: Validate → prepare payload → call API → refresh
- ✅ Chỉ gửi fields có dữ liệu (trim empty strings)
- ✅ Convert timestamps thành MySQL date format
- ✅ Type-safe với User interface
- ✅ Loading states và error handling

#### **Date Handling:**
- ✅ `parseDate()`: Support multiple formats (DD/MM/YYYY, DD-MM-YYYY, YYYY-MM-DD)
- ✅ `formatDateForAPI()`: Convert timestamp → YYYY-MM-DD
- ✅ Handle null/undefined values

#### **Options:**
- ✅ Computed options với i18n support
- ✅ Reactive khi language thay đổi

### 4. **Benefits**

#### **Reusability:**
- ✅ Có thể dùng trong components khác
- ✅ Logic tách biệt khỏi UI
- ✅ Easy to test

#### **Maintainability:**
- ✅ Single responsibility
- ✅ Clear separation of concerns
- ✅ Type-safe với TypeScript

#### **Performance:**
- ✅ Computed options (chỉ re-compute khi cần)
- ✅ Efficient data loading
- ✅ Proper loading states

### 5. **Usage Flow**

```typescript
// 1. Initialize composable
const { formData, handleSubmit, initializeForm } = useEditProfile();

// 2. Initialize form data
await initializeForm();
// → getCurrentUser() → loadUserData() → loadScannedData()

// 3. User interacts with form
// → formData automatically updates via v-model

// 4. Submit form
await handleSubmit();
// → validate → prepare payload → updateUser() → refresh
```

### 6. **Testing Checklist**

#### **Data Loading:**
- [ ] Load user data từ API
- [ ] Load scan data từ localStorage
- [ ] Handle missing data gracefully
- [ ] Show appropriate toasts

#### **Form Interaction:**
- [ ] v-model binding hoạt động
- [ ] Options render correctly
- [ ] Date pickers work
- [ ] Validation works

#### **Submission:**
- [ ] Submit với dữ liệu hợp lệ
- [ ] Handle API errors
- [ ] Loading states
- [ ] Success/error toasts

#### **Edge Cases:**
- [ ] Empty form submission
- [ ] Invalid date formats
- [ ] Network errors
- [ ] User not found

## 🎯 **Result:**
- ✅ Code giảm từ 383 → ~80 lines trong component
- ✅ Logic tách biệt và reusable
- ✅ Type-safe và maintainable
- ✅ Full functionality preserved
