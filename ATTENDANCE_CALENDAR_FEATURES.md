# Tính năng Bảng chấm công đã được cập nhật

## C<PERSON><PERSON> tính năng mới đã thêm:

### 1. <PERSON><PERSON><PERSON> tra quyền quản lý
- S<PERSON> dụng `useAuthRoleStore` để lấy thông tin role của user hiện tại
- Sử dụng helper function `isManager()` để kiểm tra user có phải quản lý không
- Chỉ hiển thị phần filter cho manager khi user có quyền quản lý

### 2. <PERSON><PERSON> lọc cho Manager
- **Chọn nhân viên**: Dropdown để chọn nhân viên cụ thể hoặc "Tất cả nhân viên"
- **Chọn tháng**: Input type="month" để chọn tháng/năm cần xem
- Sử dụng `useStaff()` composable để lấy danh sách nhân viên

### 3. Hiển thị trạng thái chấm công
- **Border đỏ**: <PERSON><PERSON><PERSON> có ca làm việc nhưng chưa chấm công
- **Background đỏ nhạt**: <PERSON><PERSON> dễ nhận biết ngày thiếu chấm công
- **Background xanh**: Ngày đã chấm công đầy đủ
- **Background xanh đậm**: Ngày hiện tại

### 4. Dữ liệu mẫu để test
Đã thêm dữ liệu mẫu cho tháng 1/2024 với:
- Các ngày có ca làm việc và đã chấm công
- Các ngày có ca làm việc nhưng chưa chấm công (hiển thị border đỏ)
- Các loại ca khác nhau: admin, overtime, holiday

### 5. Navigation tháng
- Nút Previous/Next để chuyển tháng
- Tự động cập nhật selectedMonth khi chuyển tháng
- Hiển thị tên tháng/năm bằng tiếng Việt

### 6. Legend (Chú thích)
Đã bỏ comment và hiển thị legend với:
- Ca hành chính (xanh nhạt)
- Ca tăng ca (xanh đậm)
- Hôm nay (xanh dương)
- Nghỉ (xám)
- **Chưa chấm công (border đỏ)** - Mới thêm

## Cấu trúc code:

### Template
- Filter section chỉ hiển thị cho manager
- Calendar grid với styling động
- Legend với chú thích đầy đủ

### Script
- Import các composables và helpers cần thiết
- State management cho filters
- Methods xử lý events
- Logic load dữ liệu (placeholder cho API calls)

### Styling
- Sử dụng Tailwind CSS
- Border đỏ cho ngày thiếu chấm công
- Responsive design

## Cách test:

1. Truy cập route: `/vi/attendance-calendar-demo`
2. Nếu user là manager: sẽ thấy bộ lọc nhân viên và tháng
3. Nếu user là employee: chỉ thấy calendar của mình
4. Các ngày có border đỏ là ngày có ca nhưng chưa chấm công

## API Integration (TODO):

- Kết nối với `getAttendanceMonthly()` service
- Truyền filters (staff_id, date_from, date_to)
- Xử lý response và cập nhật calendar
- Error handling

## Files đã sửa đổi:

1. `src/components/attendance/ErrorPerMonth.vue` - Component chính
2. `src/views/AttendanceCalendarDemo.vue` - Demo page
3. `src/router/localized-routes.ts` - Thêm route demo
