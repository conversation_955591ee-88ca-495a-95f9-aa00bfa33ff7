import { DEFAULT_LOCALE, type SupportedLocale } from '@/constants/locales';

/**
 * Format a date to a string in locale-specific format
 * @param date Date to format
 * @param locale Locale to use for formatting ('vi', 'en', 'ja')
 * @returns Formatted date string
 */
export function formatDate(
  date: string | number | Date,
  locale: SupportedLocale = DEFAULT_LOCALE,
): string {
  let options: Intl.DateTimeFormatOptions;

  switch (locale) {
    case 'vi':
      // dd/mm/yyyy
      options = { day: '2-digit', month: '2-digit', year: 'numeric' };
      break;
    case 'en':
      // mm/dd/yyyy
      options = { month: '2-digit', day: '2-digit', year: 'numeric' };
      break;
    case 'ja':
      // yyyy/mm/dd
      options = { year: 'numeric', month: '2-digit', day: '2-digit' };
      break;
    default:
      options = { year: 'numeric', month: 'long', day: 'numeric' };
  }

  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat(
    locale === 'vi' ? 'vi-VN' : locale === 'ja' ? 'ja-JP' : 'en-US',
    options,
  ).format(dateObj);
}

/**
 * Format a date to a full date string with weekday, month, day, and year
 * @param date Date to format (string, number, or Date object)
 * @param locale Locale to use for formatting ('vi', 'en', 'ja')
 * @returns Formatted full date string
 */
export function formatFullDate(
  date: string | number | Date | null | undefined,
  locale: SupportedLocale = DEFAULT_LOCALE,
): string {
  if (typeof date === 'string' && date.toLowerCase() === 'all day') {
    return 'All day'
  }

  if (!date) return 'NaN'

  const dateObj =
    typeof date === 'string' || typeof date === 'number'
      ? new Date(date)
      : date

  if (isNaN(dateObj.getTime())) return 'NaN'

  let options: Intl.DateTimeFormatOptions
  let localeCode: string

  switch (locale) {
    case 'vi':
      options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }
      localeCode = 'vi-VN'
      break
    case 'en':
      options = {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric',
      }
      localeCode = 'en-US'
      break
    case 'ja':
      options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long',
      }
      localeCode = 'ja-JP'
      break
    default:
      options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }
      localeCode = 'en-US'
  }

  return new Intl.DateTimeFormat(localeCode, options).format(dateObj)
}

/**
 * Convert a Date object to a MySQL timestamp string
 * @param date Date to convert
 * @returns MySQL timestamp string in 'YYYY-MM-DD HH:mm:ss' format
 */
export function toMySQLTimestamp(date: number | null): string | null {
  if (!date) return null;
  const d = new Date(date);
  const pad = (n: number) => n.toString().padStart(2, '0');
  return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(
    d.getHours(),
  )}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
}

/**
 * Format a timestamp to a time string in HH:mm format
* @param timestamp Timestamp to format
 * @returns Formatted time string
 */
// HH:mm
export const formatTime = (timestamp: number | null |string): string => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${hours}:${minutes}`;
};

/**
 * Convert a time string in HH:mm:ss format to a timestamp
 * @param timeStr Time string to convert
 * @returns Timestamp in milliseconds
 */
export const convertTimeStringToTimestamp = (timeStr: string): number => {
  const today = new Date();
  const [hours, minutes, seconds] = timeStr.split(':').map(Number);

  today.setHours(hours || 0, minutes || 0, seconds || 0, 0);
  return today.getTime();
};

/**
 * Parse a timestamp string to a number (milliseconds since epoch)
 * @param timestamp Timestamp string to parse
 * @returns Parsed timestamp in milliseconds, or null if invalid
 */
export function parseTimestamp(
  timestamp: string | null | undefined,
): number | null {
  if (!timestamp) {
    return null;
  }

  const date = new Date(timestamp);

  if (isNaN(date.getTime())) {
    return null;
  }

  return date.getTime();
}

/**
 * Format a date to a localized string with weekday, year, month, and day
 * @param date Date to format
 * @param locale Locale to use for formatting ('vi', 'en', 'ja')
 * @returns Formatted date string
 */
export function formatDateLocalized(
  date: Date | string,
  locale: SupportedLocale,
): string {
  const d = typeof date === 'string' ? new Date(date) : date;

  return new Intl.DateTimeFormat(locale, {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(d);
}

/**
 * Format a date safely. Returns empty string if date is invalid.
 * @param date Date to format
 * @param locale Locale code ('vi', 'en', 'ja')
 * @returns Formatted date or empty string
 */
export function formatDateEmployee(
  date: string | number | Date | null | undefined,
  locale: SupportedLocale = DEFAULT_LOCALE,
): string {
  if (!date) return ''

  const dateObj = new Date(date)
  if (isNaN(dateObj.getTime())) return ''
  let options: Intl.DateTimeFormatOptions

  switch (locale) {
    case 'vi':
      options = { day: '2-digit', month: '2-digit', year: 'numeric' }
      break
    case 'en':
      options = { month: '2-digit', day: '2-digit', year: 'numeric' }
      break
    case 'ja':
      options = { year: 'numeric', month: '2-digit', day: '2-digit' }
      break
    default:
      options = { year: 'numeric', month: 'long', day: 'numeric' }
  }

  return new Intl.DateTimeFormat(
    locale === 'vi' ? 'vi-VN' : locale === 'ja' ? 'ja-JP' : 'en-US',
    options,
  ).format(dateObj)
}

export const parseDate = (dateStr: string): number | null => {
  if (!dateStr) return null;
  const formats = [
    /^(\d{2})\/(\d{2})\/(\d{4})$/, // DD/MM/YYYY
    /^(\d{2})-(\d{2})-(\d{4})$/,  // DD-MM-YYYY
    /^(\d{4})-(\d{2})-(\d{2})$/   // YYYY-MM-DD
  ];

  for (const format of formats) {
    const match = dateStr.match(format);
    if (match) {
      let day, month, year;
      if (format === formats[2]) { // YYYY-MM-DD
        [, year, month, day] = match;
      } else { // DD/MM/YYYY or DD-MM-YYYY
        [, day, month, year] = match;
      }

      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      return date.getTime();
    }
  }

  return null;
};
