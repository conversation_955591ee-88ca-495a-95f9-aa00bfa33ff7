import type { FormInst, FormRules } from 'naive-ui';
import { FormMode, RegistrationType } from '@/enums';
import type { Overtime, OvertimeEmployee, OvertimeRequest } from '@/interfaces/attendance';
import { computed, reactive, ref, watch } from 'vue';
import { convertTimeStringToTimestamp, formatTime } from '@/utils/format';

import { ATTENDANCE } from '@/constants/routes';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { toast } from 'vue-sonner';
import { useAttendance } from './useAttendance';
import { useI18n } from 'vue-i18n';
import { useLeaveRequests } from '@/composables/useLeaveRequests';
import { useLocale } from './useLocale';
import { useQueryClient } from '@tanstack/vue-query';
import { useRouter } from 'vue-router';
import { useStaff } from './useStaff';
import { z } from 'zod';

interface OvertimeFormData {
  shift_id: number | null;
  employees: number[];
  time_in: number | null;
  time_out: number | null;
  timekeeping_value: string;
  approver_id: number | null;
  additional_day: string | null;
  reason: string;
}

export const useAttendanceForm = (
  mode: FormMode = FormMode.CREATE,
  overtimeId?: number | string,
) => {
  const { t } = useI18n();
  const {
    registerOvertimeMutation,
    updateWorkShiftMutation,
    updateWorkShiftGroupMutation,
    workShiftQuery,
    overtimeItemQuery,
    groupOvertimeItemQuery,
  } = useAttendance();
  const queryClient = useQueryClient();
  const { staffRemainQuery } = useLeaveRequests();
  const { staffQuery,staffApprovedQuery } = useStaff();
  const { locale } = useLocale();
  const router = useRouter();
  const hasEmployees = ref<boolean>(false);

const isGroup = typeof overtimeId === 'string' && overtimeId.startsWith('GRP-');
const {
  data: overtimeData,
  isLoading: overtimeLoading,
} = mode === FormMode.UPDATE && overtimeId
  ? isGroup
    ? groupOvertimeItemQuery(overtimeId)
    : overtimeItemQuery(Number(overtimeId))
  : { data: ref(null), isLoading: ref(false) };

  const staffList = computed(() => staffQuery.data?.value || []);
  const workShiftList = computed(() => workShiftQuery.data?.value || []);
  const staffApproveList = computed(() => staffApprovedQuery.data?.value || []);


  const staffOptions = computed(() => {
    if (
      staffRemainQuery.data?.value &&
      Array.isArray(staffRemainQuery.data.value)
    ) {
      return staffRemainQuery.data.value.map((staff) => ({
        label: staff.full_name,
        value: staff.staff_id,
      }));
    }
    return [];
  });

  const allStaffOptions = computed(() => {
    return staffList.value.map((staff) => ({
      label: staff.staff_fullname_vn,
      value: Number(staff.staff_id),
    }));
  });
    const staffApproveOptions = computed(() =>
      staffApproveList.value.map((staff) => ({
        label: staff.staff_fullname_vn,
        value: Number(staff.staff_id),
      })),
    );
  const workShiftOptions = computed(() => {
    return (
      workShiftList.value.map((shift) => ({
        label: shift.shift_code,
        value: shift.id,
      })) || []
    );
  });

  const createInitialFormData = (): OvertimeFormData => ({
    shift_id: null,
    employees: [],
    time_in: null,
    time_out: null,
    timekeeping_value: '',
    approver_id: null,
    additional_day: null,
    reason: '',
  });

  const formValue = ref<OvertimeFormData>(createInitialFormData());
  const formRef = ref<FormInst | null>(null);

  const alert = reactive({
    visible: false,
    title: '',
    message: '',
    type: 'success' as 'success' | 'error',
  });

  const rules = computed(
    (): FormRules => ({
      shift_id: {
        required: true,
        type: 'number',
        message: t('attendance.validation.shift_required'),
        trigger: ['blur', 'change'],
      },
      approver_id: {
        required: true,
        type: 'number',
        message: t('attendance.validation.approver_required'),
        trigger: ['blur', 'change'],
      },
      additional_day: {
        required: true,
        message: t('attendance.validation.additional_day_required'),
        trigger: ['blur', 'change'],
      },
      session: {
        required: true,
        type: 'number',
        message: t('attendance.validation.session_required'),
        trigger: ['blur', 'change'],
      },
      reason: {
        required: true,
        message: t('attendance.validation.reason_required'),
        trigger: ['blur', 'change'],
      },
    }),
  );

  const isLoading = computed(
    () => mode === FormMode.UPDATE && overtimeLoading.value,
  );

  const groupMembers = computed(() => {
    if (overtimeData.value?.employees && Array.isArray(overtimeData.value.employees)) {
      return overtimeData.value.employees.map((emp: OvertimeEmployee) => ({
        id: emp.employee_id,
        full_name: emp.employee_info.full_name,
        staff_identifi: emp.employee_info.staff_identifi,
        email: emp.employee_info.email,
        employee_status: emp.employee_status,
        employee_status_text: emp.employee_status_text
      }));
    }
    return [];
  });

  const isGroupOvertime = computed(() => groupMembers.value.length > 0);

  const convertToTimestamp = (timeString: string | null): number | null => {
    if (!timeString) return null;

    try {
      if (timeString.includes(':') && !timeString.includes(' ')) {
        const [hours, minutes] = timeString.split(':').map(Number);
        if (isNaN(hours) || isNaN(minutes)) return null;

        const today = new Date();
        today.setHours(hours, minutes, 0, 0);
        return today.getTime();
      }

      const date = new Date(timeString);
      return isNaN(date.getTime()) ? null : date.getTime();
    } catch (error) {
      console.warn('Error converting time string to timestamp:', timeString, error);
      return null;
    }
  };

const populateForm = () => {
  const data = overtimeData.value;
  if (!data) return;

  if (isGroup && data.group_info?.registration_type===RegistrationType.GROUP) {
    const source = data.group_info;

    const employeeIds = (data.employees || []).map((emp) => emp.employee_id);
    hasEmployees.value = employeeIds.length > 0;

    formValue.value = {
      shift_id: null,
      employees: employeeIds,
      time_in: convertToTimestamp(source.time_in || null),
      time_out: convertToTimestamp(source.time_out || null),
      timekeeping_value:
        source.timekeeping_value != null
          ? source.timekeeping_value.toString()
          : '',
      approver_id: source.approver != null ? Number(source.approver) : null,
      additional_day: source.additional_day || null,
      reason: source.reason || '',
    };
  } else {
    const source = data as Overtime;

    formValue.value = {
      shift_id: null,
      employees:  [],
      time_in: convertToTimestamp(source.time_in || null),
      time_out: convertToTimestamp(source.time_out || null),
      timekeeping_value:
        source.timekeeping_value != null
          ? source.timekeeping_value.toString()
          : '',
      approver_id: source.approver != null ? Number(source.approver) : null,
      additional_day: source.additional_day || null,
      reason: source.reason || '',
    };
  }
};
  if (mode === FormMode.UPDATE) {
    watch(
      overtimeData,
      (newData) => {
        if (newData) {
          populateForm();
        }
      },
      { immediate: true },
    );
  }

  watch(
    () => [formValue.value.time_in, formValue.value.time_out],
    ([timeIn, timeOut]) => {
      if (timeIn && timeOut) {
        let diffMs = timeOut - timeIn;
        if (diffMs < 0) {
          diffMs += 24 * 60 * 60 * 1000;
        }
        const diffHours = (diffMs / 3600000).toFixed(1);
        formValue.value.timekeeping_value = diffHours;
      } else {
        formValue.value.timekeeping_value = '';
      }
    },
  );

  watch(
    () => formValue.value.shift_id,
    (newShiftId) => {
      if (newShiftId) {
        const selectedShift = workShiftList.value.find(
          (shift) => shift.id === newShiftId,
        );

        if (selectedShift) {
          formValue.value.time_in = convertTimeStringToTimestamp(
            selectedShift.time_start,
          );
          formValue.value.time_out = convertTimeStringToTimestamp(
            selectedShift.time_end,
          );
          formValue.value.reason = selectedShift.description;
        }
      } else {
        formValue.value.time_in = null;
        formValue.value.time_out = null;
        formValue.value.timekeeping_value = '';
        formValue.value.reason = '';
      }
    },
  );

  const handleCreate = async () => {
    try {
      await formRef.value?.validate();

      const timeIn = formValue.value.time_in;
      const timeOut = formValue.value.time_out;

      let timekeepingValue = '';
      if (timeIn && timeOut) {
        let diffMs = timeOut - timeIn;
        if (diffMs < 0) {
          diffMs += 24 * 60 * 60 * 1000;
        }
        timekeepingValue = (diffMs / 3600000).toFixed(1);
        formValue.value.timekeeping_value = timekeepingValue;
      }

      const payload: OvertimeRequest = {
        employees: formValue.value.employees || [],
        additional_day: formValue.value.additional_day || '',
        time_in: timeIn ? formatTime(timeIn) : '',
        time_out: timeOut ? formatTime(timeOut) : '',
        timekeeping_value: timekeepingValue || '0',
        approver: formValue.value.approver_id || 0,
        reason: formValue.value.reason || '',
      };

      registerOvertimeMutation.mutate(payload, {
        onSuccess: () => {
          toast.success(
            t('common.toast.success.create', {
              type: t('common.module_type.overtime'),
            }),
          );

          queryClient.invalidateQueries({
            queryKey: ['latest-overtime-requests'],
          });
          queryClient.invalidateQueries({
            queryKey: ['overtime-requests'],
          });

          formValue.value = createInitialFormData();
          const newPath = getLocalizedPath(ATTENDANCE, locale.value);
          router.push(newPath);
        },
        onError: () => {
          toast.error(
            t('common.toast.error.create', {
              type: t('common.module_type.overtime'),
            }),
          );
        },
      });
    } catch (error) {
      alert.visible = true;
      alert.title = t('common.alert.error');
      alert.type = 'error';

      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map((err) => err.message).join('; ');
        alert.message = t('common.alert.validation_error', {
          errors: errorMessages,
        });
      } else {
        alert.message = t('common.alert.error_message');
      }
    }
  };

const handleUpdate = async () => {
  // if (!overtimeId) return;

  try {
    await formRef.value?.validate();

    const timeIn = formValue.value.time_in;
    const timeOut = formValue.value.time_out;

    let timekeepingValue = '';
    if (timeIn && timeOut) {
      let diffMs = timeOut - timeIn;
      if (diffMs < 0) {
        diffMs += 24 * 60 * 60 * 1000;
      }
      timekeepingValue = (diffMs / 3600000).toFixed(1);
      formValue.value.timekeeping_value = timekeepingValue;
    }

    const payload: OvertimeRequest = {
      additional_day: formValue.value.additional_day || '',
      employees: formValue.value.employees || [],
      time_in: timeIn ? formatTime(timeIn) : '',
      time_out: timeOut ? formatTime(timeOut) : '',
      timekeeping_value: timekeepingValue || '0',
      approver: formValue.value.approver_id || 0,
      reason: formValue.value.reason || '',
    };
    const isGroup = typeof overtimeId === 'string' && overtimeId.startsWith('GRP-');
    if (isGroup) {
      updateWorkShiftGroupMutation.mutate(
        {
          group_code: overtimeId,
          data: payload,
        },
        {
          onSuccess: () => {
            toast.success(t('common.toast.success.update', {
              type: t('common.module_type.overtime'),
            }));
            router.back();
          },
          onError: () => {
            toast.error(t('common.toast.error.update', {
              type: t('common.module_type.overtime'),
            }));
          },
        }
      );
    } else {
      updateWorkShiftMutation.mutate(
        {
          id: Number(overtimeId),
          data: payload,
        },
        {
          onSuccess: () => {
            toast.success(t('common.toast.success.update', {
              type: t('common.module_type.overtime'),
            }));
            router.back();
          },
          onError: () => {
            toast.error(t('common.toast.error.update', {
              type: t('common.module_type.overtime'),
            }));
          },
        }
      );
    }
  } catch (error) {
    alert.visible = true;
    alert.title = t('common.alert.error');
    alert.type = 'error';

    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => err.message).join('; ');
      alert.message = t('common.alert.validation_error', {
        errors: errorMessages,
      });
    } else {
      alert.message = t('common.alert.error_message');
    }
  }
};



  const handleSubmit = mode === FormMode.CREATE ? handleCreate : handleUpdate;

  const handleCancel = () => {
    router.back();
  };

  const updateFormValue = (newFormValue: any) => {
    Object.assign(formValue.value, newFormValue);
  };

  const updateAlert = (newAlert: any) => {
    Object.assign(alert, newAlert);
  };

  return {
    formValue,
    formRef,
    rules,
    alert,
    hasEmployees,
    allStaffOptions,
    staffOptions,
    workShiftOptions,
    workShiftList,
    staffApproveOptions,
    isLoading,
    groupMembers,
    isGroupOvertime,

    handleSubmit,
    handleCancel,
    populateForm,
    updateFormValue,
    updateAlert,

    registerOvertimeMutation,
    updateWorkShiftMutation,
    updateWorkShiftGroupMutation,
  };
};
