<script setup lang="ts">
import ErrorPerMonth from '@/components/attendance/ErrorPerMonth.vue';
import RecentRequest from '@/components/attendance/RecentRequest.vue';
import WorkSchedule from '@/components/attendance/WorkSchedule.vue';
import { IonPage } from '@ionic/vue';
</script>

<template>
  <ion-page class="ion-padding">
    <div class="scroll-container flex h-full flex-col items-center gap-y-4">
      <WorkSchedule />
      <ErrorPerMonth />
      <RecentRequest />
    </div>
  </ion-page>
</template>
