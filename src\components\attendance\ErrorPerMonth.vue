<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthRoleStore } from '@/stores/auth-role'
import { isManager } from '@/helpers/staff-helper'
import { useI18n } from 'vue-i18n'
import { useAttendanceForm } from '@/composables/useAttendanceForm'
import { useDateFormats } from '@/composables/useDateFormats'
import { NSelect, NFormItem, NForm, NTimePicker, NInput } from 'naive-ui'
import { Button } from '@/components/ui/button'
import CalendarHeader from '@/components/ui/calendar/CalendarHeader.vue'

interface DateInfo {
  date: number
  month: number
  year: number
  isCurrentMonth: boolean
  isToday: boolean
  shiftCode?: string
  attendance?: string
  hasAttendance?: boolean
  // shiftType?: 'admin' | 'overtime' | 'holiday'
}

interface ShiftInfo {
  shiftCode: string
  shiftName: string
  timeStart: string
  timeEnd: string
  attendance?: string
  shiftType: 'admin' | 'overtime' | 'holiday'
}

interface OvertimeFormData {
  date: string
  timeIn: number | null
  timeOut: number | null
  reason: string
  type: 'before' | 'after' // trước ca hoặc sau ca
}

const { t } = useI18n()
const { formValue, staffOptions } = useAttendanceForm()
const { pickerConfigs } = useDateFormats()

const authRoleStore = useAuthRoleStore()

const isManagerUser = computed(() => {
  return isManager(authRoleStore.roleName || '')
})

const currentDate = ref(new Date())
const selectedDate = ref<DateInfo | null>(null)
const selectedStaffId = ref<string>('')
const selectedMonth = ref<string>('')
// const attendanceData = ref<any[]>([])
const isLoading = ref(false)

// Overtime registration state
const showOvertimeForm = ref(false)
const overtimeFormData = ref<OvertimeFormData>({
  date: '',
  timeIn: null,
  timeOut: null,
  reason: '',
  type: 'before'
})
const overtimeFormRef = ref()

const daysOfWeek = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7']

// Initialize selected month with current month
// const initializeSelectedMonth = () => {
//   const now = new Date()
//   const year = now.getFullYear()
//   const month = (now.getMonth() + 1).toString().padStart(2, '0')
//   selectedMonth.value = `${year}-${month}`
// }

// Sample work schedule data with shift times
const workSchedule = ref<Record<string, ShiftInfo>>({
  '2025-8-9': { shiftCode: 'A7', shiftName: 'Ca hành chính A7', timeStart: '08:00', timeEnd: '17:00', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-7-10': { shiftCode: 'BB', shiftName: 'Ca hành chính BB', timeStart: '07:30', timeEnd: '16:30', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-8-11': { shiftCode: 'CMA7', shiftName: 'Ca tăng ca CMA7', timeStart: '18:00', timeEnd: '22:00', attendance: 'Đã chấm', shiftType: 'overtime' },
  '2025-8-12': { shiftCode: 'TSHC2', shiftName: 'Ca tăng ca TSHC2', timeStart: '19:00', timeEnd: '23:00', shiftType: 'overtime' }, // Missing attendance
  '2025-8-6': { shiftCode: 'TSHO2', shiftName: 'Ca tăng ca TSHO2', timeStart: '20:00', timeEnd: '24:00', attendance: 'Đã chấm', shiftType: 'overtime' },
  '2025-8-14': { shiftCode: 'A1', shiftName: 'Ca hành chính A1', timeStart: '08:00', timeEnd: '17:00', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-8-15': { shiftCode: 'A4', shiftName: 'Ca hành chính A4', timeStart: '09:00', timeEnd: '18:00', shiftType: 'admin' }, // Missing attendance
  '2025-8-16': { shiftCode: 'A2', shiftName: 'Ca hành chính A2', timeStart: '08:30', timeEnd: '17:30', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-8-17': { shiftCode: 'B2', shiftName: 'Ca hành chính B2', timeStart: '13:00', timeEnd: '22:00', shiftType: 'admin' }, // Missing attendance
  '2025-7-18': { shiftCode: 'B3', shiftName: 'Ca hành chính B3', timeStart: '14:00', timeEnd: '23:00', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-8-19': { shiftCode: 'CM1', shiftName: 'Ca chiều CM1', timeStart: '13:30', timeEnd: '22:30', shiftType: 'admin' }, // Missing attendance
  '2025-8-20': { shiftCode: 'CM2', shiftName: 'Ca chiều CM2', timeStart: '14:30', timeEnd: '23:30', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-8-21': { shiftCode: 'A6', shiftName: 'Ca hành chính A6', timeStart: '06:00', timeEnd: '15:00', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-8-22': { shiftCode: 'A8', shiftName: 'Ca hành chính A8', timeStart: '10:00', timeEnd: '19:00', shiftType: 'admin' }, // Missing attendance
  '2025-7-23': { shiftCode: 'B1', shiftName: 'Ca hành chính B1', timeStart: '12:00', timeEnd: '21:00', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-8-24': { shiftCode: 'B4', shiftName: 'Ca hành chính B4', timeStart: '15:00', timeEnd: '24:00', shiftType: 'admin' }, // Missing attendance
  '2025-7-25': { shiftCode: 'CMA1', shiftName: 'Ca tăng ca CMA1', timeStart: '17:30', timeEnd: '21:30', attendance: 'Đã chấm', shiftType: 'overtime' },
  '2025-7-26': { shiftCode: 'CMA2', shiftName: 'Ca tăng ca CMA2', timeStart: '18:30', timeEnd: '22:30', shiftType: 'overtime' }, // Missing attendance
  '2025-7-27': { shiftCode: 'TSHC1', shiftName: 'Ca tăng ca TSHC1', timeStart: '19:30', timeEnd: '23:30', attendance: 'Đã chấm', shiftType: 'overtime' },
  '2025-8-28': { shiftCode: 'TSHO1', shiftName: 'Ca tăng ca TSHO1', timeStart: '20:30', timeEnd: '00:30', shiftType: 'overtime' }, // Missing attendance
  '2025-8-29': { shiftCode: 'A1', shiftName: 'Ca hành chính A1', timeStart: '08:00', timeEnd: '17:00', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-8-30': { shiftCode: 'A9', shiftName: 'Ca hành chính A9', timeStart: '11:00', timeEnd: '20:00', shiftType: 'admin' }, // Missing attendance
})

const calendarDates = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  const firstDay = new Date(year, month, 1)
  // const lastDay = new Date(year, month + 1, 0)
  const startDate = new Date(firstDay)

  // Adjust to start from Sunday (0) instead of Monday
  const dayOfWeek = firstDay.getDay()
  startDate.setDate(startDate.getDate() - dayOfWeek)

  const dates: DateInfo[] = []
  const today = new Date()

  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)

    const dateKey = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
    const scheduleInfo = workSchedule.value[dateKey]

    dates.push({
      date: date.getDate(),
      month: date.getMonth(),
      year: date.getFullYear(),
      isCurrentMonth: date.getMonth() === month,
      isToday: date.toDateString() === today.toDateString(),
      shiftCode: scheduleInfo?.shiftCode,
      attendance: scheduleInfo?.attendance,
      hasAttendance: !!scheduleInfo?.attendance,
      // shiftType: scheduleInfo?.shiftType
    })
  }

  return dates
})

const getCellClasses = (date: DateInfo): string => {
  const classes = ['hover:bg-gray-100', 'rounded-sm',]

  if (!date.isCurrentMonth) {
    classes.push('text-gray-400', 'border-transparent')
  } else {
    // Check if has shift but no attendance (missing attendance)
    if (date.shiftCode && !date.hasAttendance) {
      classes.push('border-red-200', 'bg-red-50')
    }
  }

  if (date.isToday) {
    classes.push('bg-blue-500', 'text-white', 'border-blue-500')
  }
  return classes.join(' ')
}

const getShiftName = (shiftCode: string): string => {
  const shiftNames: Record<string, string> = {
    'A1': 'Ca hành chính A1',
    'A2': 'Ca hành chính A2',
    'A5': 'Ca hành chính A5',
    'A6': 'Ca hành chính A6',
    'A7': 'Ca hành chính A7',
    'B1': 'Ca hành chính B1',
    'B5': 'Ca hành chính B5',
    'BB': 'Ca hành chính BB',
    'CM3': 'Ca chiều CM3',
    'CMA7': 'Ca tăng ca CMA7',
    'HO': 'Ca nghỉ lễ',
    'TSHC2': 'Ca tăng ca TSHC2',
    'TSHO1': 'Ca tăng ca TSHO1',
    'TSHO2': 'Ca tăng ca TSHO2'
  }
  return shiftNames[shiftCode] || shiftCode
}

// Check if selected date has work shift
const hasWorkShift = computed(() => {
  if (!selectedDate.value) return false
  const dateKey = `${selectedDate.value.year}-${selectedDate.value.month + 1}-${selectedDate.value.date}`
  return !!workSchedule.value[dateKey]
})

// Get shift info for selected date
const selectedShiftInfo = computed(() => {
  if (!selectedDate.value) return null
  const dateKey = `${selectedDate.value.year}-${selectedDate.value.month + 1}-${selectedDate.value.date}`
  return workSchedule.value[dateKey] || null
})

const selectDate = (date: DateInfo): void => {
  if (date.isCurrentMonth) {
    selectedDate.value = date

    // Check if this date has a work shift
    const dateKey = `${date.year}-${date.month + 1}-${date.date}`
    const shiftInfo = workSchedule.value[dateKey]

    if (shiftInfo) {
      // Show overtime registration form
      showOvertimeForm.value = true
      overtimeFormData.value.date = `${date.year}-${(date.month + 1).toString().padStart(2, '0')}-${date.date.toString().padStart(2, '0')}`
    } else {
      // Hide overtime form if no shift
      showOvertimeForm.value = false
    }
  }
}

// Close overtime form and return to calendar
const closeOvertimeForm = (): void => {
  showOvertimeForm.value = false
  selectedDate.value = null
  // Reset form data
  overtimeFormData.value = {
    date: '',
    timeIn: null,
    timeOut: null,
    reason: '',
    type: 'before'
  }
}

// Submit overtime registration
const submitOvertimeForm = (): void => {
  closeOvertimeForm()
}

// Navigation methods
const previousMonth = (): void => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
  updateSelectedMonth()
}

const nextMonth = (): void => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
  updateSelectedMonth()
}

const updateSelectedMonth = (): void => {
  const year = currentDate.value.getFullYear()
  const month = (currentDate.value.getMonth() + 1).toString().padStart(2, '0')
  selectedMonth.value = `${year}-${month}`
}

const onMonthChange = (): void => {
  if (selectedMonth.value) {
    const [year, month] = selectedMonth.value.split('-')
    currentDate.value = new Date(parseInt(year), parseInt(month) - 1, 1)
    loadAttendanceData()
  }
}
const loadAttendanceData = async (): Promise<void> => {
  if (!isManagerUser.value) return

  isLoading.value = true
  try {
    const filters: any = {}

    if (selectedStaffId.value) {
      filters.staff_id = selectedStaffId.value
    }

    if (selectedMonth.value) {
      const [year, month] = selectedMonth.value.split('-')
      filters.date_from = `${year}-${month}-01`
      // Get last day of month
      const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate()
      filters.date_to = `${year}-${month}-${lastDay.toString().padStart(2, '0')}`
    }

    // TODO: Implement API call to load attendance data
  } catch (error) {
    console.error('Error loading attendance data:', error)
  } finally {
    isLoading.value = false
  }
}

// Initialize data
const initializeData = (): void => {
  // Initialize selected month
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  selectedMonth.value = `${year}-${month}`

  // Set current date as selected by default
  const today = new Date()
  const todayKey = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`
  const todaySchedule = workSchedule.value[todayKey]

  if (todaySchedule) {
    selectedDate.value = {
      date: today.getDate(),
      month: today.getMonth(),
      year: today.getFullYear(),
      isCurrentMonth: true,
      isToday: true,
      shiftCode: todaySchedule.shiftCode,
      attendance: todaySchedule.attendance,
      hasAttendance: !!todaySchedule.attendance,
      // shiftType: todaySchedule.shiftType
    }
  }

  if (isManagerUser.value) {
    loadAttendanceData()
  }
}

// Watch for role changes
watch(isManagerUser, (newValue) => {
  if (newValue) {
    loadAttendanceData()
  }
})

onMounted(() => {
  initializeData()
})
</script>

<template>
  <h1 class="text-lg w-full mt-2 px-1 font-semibold">Bảng chấm công</h1>
  <div class="bg-white rounded-lg shadow-sm border px-2 py-2 w-full mx-auto">

    <!-- Overtime Registration Form -->
    <div v-if="showOvertimeForm && selectedShiftInfo" class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold">Đăng ký trước/sau giờ</h2>
        <button @click="closeOvertimeForm" class="p-2 hover:bg-gray-100 rounded">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Shift Information -->
      <div class="mb-4 p-3 bg-blue-50 rounded-lg">
        <h3 class="text-sm font-semibold mb-2">Thông tin ca làm việc</h3>
        <p class="text-sm text-gray-600">Ngày: {{ overtimeFormData.date }}</p>
        <p class="text-sm text-gray-600">Ca: {{ selectedShiftInfo.shiftName }} ({{ selectedShiftInfo.shiftCode }})</p>
        <p class="text-sm text-gray-600">Thời gian: {{ selectedShiftInfo.timeStart }} - {{ selectedShiftInfo.timeEnd }}
        </p>
      </div>

      <!-- Registration Form -->
      <n-form ref="overtimeFormRef" :model="overtimeFormData" class="space-y-4">
        <!-- Registration Type -->
        <n-form-item label="Loại đăng ký" path="type">
          <n-select v-model:value="overtimeFormData.type" :options="[
            { label: 'Trước ca làm việc', value: 'before' },
            { label: 'Sau ca làm việc', value: 'after' }
          ]" placeholder="Chọn loại đăng ký" />
        </n-form-item>

        <!-- Time Input -->
        <div class="grid grid-cols-2 gap-4">
          <n-form-item label="Giờ bắt đầu" path="timeIn">
            <n-time-picker v-model:value="overtimeFormData.timeIn" :format="pickerConfigs.attendance.time.format"
              placeholder="Chọn giờ bắt đầu" clearable class="w-full" />
          </n-form-item>

          <n-form-item label="Giờ kết thúc" path="timeOut">
            <n-time-picker v-model:value="overtimeFormData.timeOut" :format="pickerConfigs.attendance.time.format"
              placeholder="Chọn giờ kết thúc" clearable class="w-full" />
          </n-form-item>
        </div>

        <!-- Reason -->
        <n-form-item label="Lý do" path="reason">
          <n-input v-model:value="overtimeFormData.reason" type="textarea"
            placeholder="Nhập lý do đăng ký trước/sau giờ" :autosize="{ minRows: 3, maxRows: 5 }" />
        </n-form-item>

        <!-- Submit Button -->
        <div class="flex gap-2">
          <Button @click="submitOvertimeForm" class="flex-1">
            Đăng ký
          </Button>
          <Button variant="outline" @click="closeOvertimeForm" class="flex-1">
            Hủy
          </Button>
        </div>
      </n-form>
    </div>
    <!-- Filter Section for Manager -->
    <div v-if="isManagerUser" class="mb-4 p-2 rounded-lg">
      <div class="grid grid-cols-1">
        <!-- Employee Selection -->
        <div>
          <n-form-item :label="t('leaves.new_request.staff')" path="staff">
            <n-select filterable multiple v-model:value="formValue.employees" :options="staffOptions"
              :placeholder="t('leaves.new_request.staff_placeholder')" />
          </n-form-item>
        </div>
        <!-- Month Selection -->
        <div>
          <label class="block text-sm font-medium mb-2">Chọn tháng</label>
          <input type="month" v-model="selectedMonth" class="w-full px-3 py-1.5 border border-gray-300 rounded-sm"
            @change="onMonthChange" />
        </div>
      </div>
    </div>

    <!-- Header with month/year navigation -->
    <CalendarHeader :current-date="currentDate" @previous-month="previousMonth" @next-month="nextMonth" class="mb-6" />

    <!-- Days of week header -->
    <div class="grid grid-cols-7 gap-1 mb-2">
      <div v-for="day in daysOfWeek" :key="day" class="text-center text-sm font-medium text-gray-600 py-2">
        {{ day }}
      </div>
    </div>

    <!-- Calendar grid -->
    <div class="grid grid-cols-7 gap-1 gap-y-2">
      <div v-for="date in calendarDates" :key="`${date.date}-${date.month}`"
        class="aspect-square flex flex-col items-center justify-center text-sm relative cursor-pointer transition-colors"
        :class="getCellClasses(date)" @click="selectDate(date)">
        <span class="font-medium" :class="{ 'text-white': date.isToday }">
          {{ date.date }}
        </span>
        <span v-if="date.shiftCode" class="text-xs font-light mt-0.5" :class="{ 'text-white': date.isToday }">
          {{ date.shiftCode }}
        </span>
      </div>
    </div>
    <!-- Selected date info -->
    <div v-if="selectedDate" class="mt-4 p-3 bg-blue-50 rounded-lg">
      <h3 class="text-sm font-semibold mb-1">Thông tin ngày {{ selectedDate.date }}/{{ selectedDate.month + 1 }}</h3>
      <p class="text-sm text-gray-600">
        <span v-if="selectedDate.shiftCode">Ca làm việc: {{ getShiftName(selectedDate.shiftCode) }}</span>
        <span v-else>Không có ca làm việc</span>
      </p>
      <p class="text-sm text-gray-600 mt-1">
        Chấm công: {{ selectedDate.attendance || 'Chưa chấm' }}
      </p>
    </div>
  </div>
</template>
