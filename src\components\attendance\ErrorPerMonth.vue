<template>
  <h1 class="text-lg w-full mt-2 px-1 font-semibold">Bảng chấm công</h1>
  <div class="bg-white rounded-lg shadow-sm border px-4 py-2 w-full mx-auto">
    <!-- Header with month/year navigation -->
    <!-- <div class="flex items-center justify-between mb-4">
      <button @click="previousMonth" class="p-2 hover:bg-gray-100 rounded">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
      <h2 class="text-lg font-semibold">{{ monthYear }}</h2>
      <button @click="nextMonth" class="p-2 hover:bg-gray-100 rounded">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    </div> -->

    <!-- Days of week header -->
    <!-- <div class="grid grid-cols-7 gap-1 mb-2">
      <div v-for="day in daysOfWeek" :key="day" class="text-center text-sm font-medium text-gray-600 py-2">
        {{ day }}
      </div>
    </div> -->

    <!-- Calendar grid -->
    <div class="grid grid-cols-7 gap-1">
      <div v-for="date in calendarDates" :key="`${date.date}-${date.month}`"
        class="aspect-square flex flex-col items-center justify-center text-sm relative cursor-pointer transition-colors"
        :class="getCellClasses(date)" @click="selectDate(date)">
        <span class="font-medium" :class="{ 'text-sky-600': date.isToday }">
          {{ date.date }}
        </span>
        <!-- <p class="text-sm text-gray-600 mt-1">
          {{ selectedDate.attendance }}
        </p> -->
        <span v-if="date.shiftCode" class="text-xs font-semibold mt-0.5" :class="{ 'text-white': date.isToday }">
          {{ date.shiftCode }}
        </span>
      </div>
    </div>

    <!-- Legend -->
    <!-- <div class="mt-4 p-3 bg-gray-50 rounded-lg">
      <h3 class="font-semibold mb-2">Chú thích ca làm việc:</h3>
      <div class="grid grid-cols-2 gap-2">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-200 rounded mr-2"></div>
          <span>Ca hành chính</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-300 rounded mr-2"></div>
          <span>Ca tăng ca</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-blue-500 rounded mr-2"></div>
          <span>Hôm nay</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-gray-200 rounded mr-2"></div>
          <span>Nghỉ</span>
        </div>
      </div>
    </div> -->

    <!-- Selected date info -->
    <div v-if="selectedDate" class="mt-4 p-3 bg-blue-50 rounded-lg">
      <h3 class="text-sm font-semibold mb-1">Thông tin ngày {{ selectedDate.date }}/{{ selectedDate.month + 1 }}</h3>
      <p class="text-sm text-gray-600">
        <span v-if="selectedDate.shiftCode">Ca làm việc: {{ getShiftName(selectedDate.shiftCode) }}</span>
        <span v-else>Không có ca làm việc</span>
      </p>
      <p class="text-sm text-gray-600 mt-1">
        Chấm công: {{ selectedDate.attendance || 'Chưa chấm' }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface DateInfo {
  date: number
  month: number
  year: number
  isCurrentMonth: boolean
  isToday: boolean
  shiftCode?: string
  attendance?: string
  // shiftType?: 'admin' | 'overtime' | 'holiday'
}

const currentDate = ref(new Date())
const selectedDate = ref<DateInfo | null>(null)

// const daysOfWeek = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7']

// Sample work schedule data
const workSchedule = ref<Record<string, { shiftCode: string, attendance?: string, shiftType: 'admin' | 'overtime' | 'holiday' }>>({
  '2024-1-2': { shiftCode: 'A1', attendance: 'Đã chấm', shiftType: 'admin' },
  '2024-1-4': { shiftCode: 'A5', attendance: 'Đã chấm', shiftType: 'admin' },
  '2024-1-5': { shiftCode: 'B5', attendance: 'Đã chấm', shiftType: 'admin' },
  '2024-1-6': { shiftCode: 'CM3', attendance: 'Đã chấm', shiftType: 'admin' },
  '2024-1-7': { shiftCode: 'HO', attendance: 'Đã chấm', shiftType: 'holiday' },
  '2024-1-9': { shiftCode: 'A7', attendance: 'Đã chấm', shiftType: 'admin' },
  '2024-1-10': { shiftCode: 'BB', attendance: 'Đã chấm', shiftType: 'admin' },
  '2024-1-11': { shiftCode: 'CMA7', attendance: 'Đã chấm', shiftType: 'overtime' },
  '2024-1-12': { shiftCode: 'TSHC2', attendance: 'Đã chấm', shiftType: 'overtime' },
  '2024-1-13': { shiftCode: 'TSHO2', attendance: 'Đã chấm', shiftType: 'overtime' },
  '2024-1-14': { shiftCode: 'A1', attendance: 'Đã chấm', shiftType: 'admin' },
  '2024-1-16': { shiftCode: 'A2', attendance: 'Đã chấm', shiftType: 'admin' },
  '2024-1-21': { shiftCode: 'A6', attendance: 'Đã chấm', shiftType: 'admin' },
  '2024-1-23': { shiftCode: 'B1', attendance: 'Đã chấm', shiftType: 'admin' },
  '2024-1-28': { shiftCode: 'TSHO1', attendance: 'Đã chấm', shiftType: 'overtime' },
  '2024-1-29': { shiftCode: 'A1', attendance: 'Đã chấm', shiftType: 'admin' },
})

// const monthYear = computed(() => {
//   return currentDate.value.toLocaleDateString('vi-VN', {
//     month: 'long',
//     year: 'numeric'
//   })
// })

const calendarDates = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  const firstDay = new Date(year, month, 1)
  // const lastDay = new Date(year, month + 1, 0)
  const startDate = new Date(firstDay)

  // Adjust to start from Sunday (0) instead of Monday
  const dayOfWeek = firstDay.getDay()
  startDate.setDate(startDate.getDate() - dayOfWeek)

  const dates: DateInfo[] = []
  const today = new Date()

  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)

    const dateKey = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
    const scheduleInfo = workSchedule.value[dateKey]

    dates.push({
      date: date.getDate(),
      month: date.getMonth(),
      year: date.getFullYear(),
      isCurrentMonth: date.getMonth() === month,
      isToday: date.toDateString() === today.toDateString(),
      shiftCode: scheduleInfo?.shiftCode,
      attendance: scheduleInfo?.attendance,
      // shiftType: scheduleInfo?.shiftType
    })
  }

  return dates
})

const getCellClasses = (date: DateInfo): string => {
  const classes = ['hover:bg-gray-100', 'border']

  if (!date.isCurrentMonth) {
    classes.push('text-gray-400', 'border-transparent')
  } else {
    classes.push('border-gray-200')
  }

  // if (date.isToday) {
  //   classes.push('bg-blue-500', 'text-white', 'border-blue-500')
  // } else if (date.shiftCode) {
  //   if (date.shiftType === 'overtime') {
  //     classes.push('bg-green-300', 'border-green-400')
  //   } else if (date.shiftType === 'holiday') {
  //     classes.push('bg-green-200', 'border-green-300')
  //   } else {
  //     classes.push('bg-green-200', 'border-green-300')
  //   }
  // }

  return classes.join(' ')
}

const getShiftName = (shiftCode: string): string => {
  const shiftNames: Record<string, string> = {
    'A1': 'Ca hành chính A1',
    'A2': 'Ca hành chính A2',
    'A5': 'Ca hành chính A5',
    'A6': 'Ca hành chính A6',
    'A7': 'Ca hành chính A7',
    'B1': 'Ca hành chính B1',
    'B5': 'Ca hành chính B5',
    'BB': 'Ca hành chính BB',
    'CM3': 'Ca chiều CM3',
    'CMA7': 'Ca tăng ca CMA7',
    'HO': 'Ca nghỉ lễ',
    'TSHC2': 'Ca tăng ca TSHC2',
    'TSHO1': 'Ca tăng ca TSHO1',
    'TSHO2': 'Ca tăng ca TSHO2'
  }
  return shiftNames[shiftCode] || shiftCode
}

const selectDate = (date: DateInfo): void => {
  if (date.isCurrentMonth) {
    selectedDate.value = date
  }
}

// const previousMonth = (): void => {
//   currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
// }

// const nextMonth = (): void => {
//   currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
// }

onMounted(() => {
  // Set current date as selected by default
  const today = new Date()
  const todayKey = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`
  const todaySchedule = workSchedule.value[todayKey]

  if (todaySchedule) {
    selectedDate.value = {
      date: today.getDate(),
      month: today.getMonth(),
      year: today.getFullYear(),
      isCurrentMonth: true,
      isToday: true,
      shiftCode: todaySchedule.shiftCode,
      attendance: todaySchedule.attendance,
      // shiftType: todaySchedule.shiftType
    }
  }
})
</script>
