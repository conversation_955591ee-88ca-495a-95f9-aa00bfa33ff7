<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthRoleStore } from '@/stores/auth-role'
// import { useStaff } from '@/composables/useStaff'
import { isManager } from '@/helpers/staff-helper'
import { useI18n } from 'vue-i18n'
import { useAttendanceForm } from '@/composables/useAttendanceForm'
import { NSelect, NFormItem } from 'naive-ui'
// import { getAttendanceMonthly } from '@/services/attendance.service'

interface DateInfo {
  date: number
  month: number
  year: number
  isCurrentMonth: boolean
  isToday: boolean
  shiftCode?: string
  attendance?: string
  hasAttendance?: boolean
  // shiftType?: 'admin' | 'overtime' | 'holiday'
}

const { t } = useI18n()
const { formValue, staffOptions } = useAttendanceForm()

// Auth and role management
const authRoleStore = useAuthRoleStore()
// const { staffQuery } = useStaff()

// Check if current user is manager
const isManagerUser = computed(() => {
  return isManager(authRoleStore.roleName || '')
})

// State management
const currentDate = ref(new Date())
const selectedDate = ref<DateInfo | null>(null)
const selectedStaffId = ref<string>('')
const selectedMonth = ref<string>('')
// const attendanceData = ref<any[]>([])
const isLoading = ref(false)

// Staff list for managers
// const staffList = computed(() => staffQuery.data.value || [])

const daysOfWeek = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7']

// Initialize selected month with current month
// const initializeSelectedMonth = () => {
//   const now = new Date()
//   const year = now.getFullYear()
//   const month = (now.getMonth() + 1).toString().padStart(2, '0')
//   selectedMonth.value = `${year}-${month}`
// }

// Sample work schedule data with more test data including missing attendance
const workSchedule = ref<Record<string, { shiftCode: string, attendance?: string, shiftType: 'admin' | 'overtime' | 'holiday' }>>({
  // January 2025 - with some missing attendance
  '2025-9-9': { shiftCode: 'A7', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-9-10': { shiftCode: 'BB', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-9-11': { shiftCode: 'CMA7', attendance: 'Đã chấm', shiftType: 'overtime' },
  '2025-9-12': { shiftCode: 'TSHC2', shiftType: 'overtime' }, // Missing attendance
  '2025-9-13': { shiftCode: 'TSHO2', attendance: 'Đã chấm', shiftType: 'overtime' },
  '2025-9-14': { shiftCode: 'A1', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-9-15': { shiftCode: 'A4', shiftType: 'admin' }, // Missing attendance
  '2025-9-16': { shiftCode: 'A2', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-9-17': { shiftCode: 'B2', shiftType: 'admin' }, // Missing attendance
  '2025-9-18': { shiftCode: 'B3', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-9-19': { shiftCode: 'CM1', shiftType: 'admin' }, // Missing attendance
  '2025-9-20': { shiftCode: 'CM2', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-9-21': { shiftCode: 'A6', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-9-22': { shiftCode: 'A8', shiftType: 'admin' }, // Missing attendance
  '2025-9-23': { shiftCode: 'B1', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-9-24': { shiftCode: 'B4', shiftType: 'admin' }, // Missing attendance
  '2025-9-25': { shiftCode: 'CMA1', attendance: 'Đã chấm', shiftType: 'overtime' },
  '2025-9-26': { shiftCode: 'CMA2', shiftType: 'overtime' }, // Missing attendance
  '2025-9-27': { shiftCode: 'TSHC1', attendance: 'Đã chấm', shiftType: 'overtime' },
  '2025-9-28': { shiftCode: 'TSHO1', shiftType: 'overtime' }, // Missing attendance
  '2025-9-29': { shiftCode: 'A1', attendance: 'Đã chấm', shiftType: 'admin' },
  '2025-9-30': { shiftCode: 'A9', shiftType: 'admin' }, // Missing attendance
  '2025-9-31': { shiftCode: 'B6', attendance: 'Đã chấm', shiftType: 'admin' },
})

const monthYear = computed(() => {
  return currentDate.value.toLocaleDateString('vi-VN', {
    month: 'long',
    year: 'numeric'
  })
})

const calendarDates = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  const firstDay = new Date(year, month, 1)
  // const lastDay = new Date(year, month + 1, 0)
  const startDate = new Date(firstDay)

  // Adjust to start from Sunday (0) instead of Monday
  const dayOfWeek = firstDay.getDay()
  startDate.setDate(startDate.getDate() - dayOfWeek)

  const dates: DateInfo[] = []
  const today = new Date()

  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)

    const dateKey = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
    const scheduleInfo = workSchedule.value[dateKey]

    dates.push({
      date: date.getDate(),
      month: date.getMonth(),
      year: date.getFullYear(),
      isCurrentMonth: date.getMonth() === month,
      isToday: date.toDateString() === today.toDateString(),
      shiftCode: scheduleInfo?.shiftCode,
      attendance: scheduleInfo?.attendance,
      hasAttendance: !!scheduleInfo?.attendance,
      // shiftType: scheduleInfo?.shiftType
    })
  }

  return dates
})

const getCellClasses = (date: DateInfo): string => {
  const classes = ['hover:bg-gray-100', 'rounded-sm',]

  if (!date.isCurrentMonth) {
    classes.push('text-gray-400', 'border-transparent')
  } else {
    // Check if has shift but no attendance (missing attendance)
    if (date.shiftCode && !date.hasAttendance) {
      classes.push('border-red-200', 'bg-red-50')
    }
  }

  if (date.isToday) {
    classes.push('bg-blue-500', 'text-white', 'border-blue-500')
  }
  return classes.join(' ')
}

const getShiftName = (shiftCode: string): string => {
  const shiftNames: Record<string, string> = {
    'A1': 'Ca hành chính A1',
    'A2': 'Ca hành chính A2',
    'A5': 'Ca hành chính A5',
    'A6': 'Ca hành chính A6',
    'A7': 'Ca hành chính A7',
    'B1': 'Ca hành chính B1',
    'B5': 'Ca hành chính B5',
    'BB': 'Ca hành chính BB',
    'CM3': 'Ca chiều CM3',
    'CMA7': 'Ca tăng ca CMA7',
    'HO': 'Ca nghỉ lễ',
    'TSHC2': 'Ca tăng ca TSHC2',
    'TSHO1': 'Ca tăng ca TSHO1',
    'TSHO2': 'Ca tăng ca TSHO2'
  }
  return shiftNames[shiftCode] || shiftCode
}

const selectDate = (date: DateInfo): void => {
  if (date.isCurrentMonth) {
    selectedDate.value = date
  }
}

// Navigation methods
const previousMonth = (): void => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
  updateSelectedMonth()
}

const nextMonth = (): void => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
  updateSelectedMonth()
}

const updateSelectedMonth = (): void => {
  const year = currentDate.value.getFullYear()
  const month = (currentDate.value.getMonth() + 1).toString().padStart(2, '0')
  selectedMonth.value = `${year}-${month}`
}

const onMonthChange = (): void => {
  if (selectedMonth.value) {
    const [year, month] = selectedMonth.value.split('-')
    currentDate.value = new Date(parseInt(year), parseInt(month) - 1, 1)
    loadAttendanceData()
  }
}
const loadAttendanceData = async (): Promise<void> => {
  if (!isManagerUser.value) return

  isLoading.value = true
  try {
    // Here you would call the API with filters
    const filters: any = {}

    if (selectedStaffId.value) {
      filters.staff_id = selectedStaffId.value
    }

    if (selectedMonth.value) {
      const [year, month] = selectedMonth.value.split('-')
      filters.date_from = `${year}-${month}-01`
      // Get last day of month
      const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate()
      filters.date_to = `${year}-${month}-${lastDay.toString().padStart(2, '0')}`
    }

    // const response = await getAttendanceMonthly(filters)
    // attendanceData.value = response.data

    // TODO: Implement API call to load attendance data
  } catch (error) {
    console.error('Error loading attendance data:', error)
  } finally {
    isLoading.value = false
  }
}

// Initialize data
const initializeData = (): void => {
  // Initialize selected month
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  selectedMonth.value = `${year}-${month}`

  // Set current date as selected by default
  const today = new Date()
  const todayKey = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`
  const todaySchedule = workSchedule.value[todayKey]

  if (todaySchedule) {
    selectedDate.value = {
      date: today.getDate(),
      month: today.getMonth(),
      year: today.getFullYear(),
      isCurrentMonth: true,
      isToday: true,
      shiftCode: todaySchedule.shiftCode,
      attendance: todaySchedule.attendance,
      hasAttendance: !!todaySchedule.attendance,
      // shiftType: todaySchedule.shiftType
    }
  }

  if (isManagerUser.value) {
    loadAttendanceData()
  }
}

// Watch for role changes
watch(isManagerUser, (newValue) => {
  if (newValue) {
    loadAttendanceData()
  }
})

onMounted(() => {
  initializeData()
})
</script>

<template>
  <h1 class="text-lg w-full mt-2 px-1 font-semibold">Bảng chấm công</h1>
  <div class="bg-white rounded-lg shadow-sm border px-2 py-2 w-full mx-auto">
    <!-- Filter Section for Manager -->
    <div v-if="isManagerUser" class="mb-4 p-2 rounded-lg">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Employee Selection -->
        <div>
          <n-form-item :label="t('leaves.new_request.staff')" path="staff">
            <n-select filterable multiple v-model:value="formValue.employees" :options="staffOptions"
              :placeholder="t('leaves.new_request.staff_placeholder')" />
          </n-form-item>
        </div>
        <!-- Month Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Chọn tháng</label>
          <input type="month" v-model="selectedMonth"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            @change="onMonthChange" />
        </div>
      </div>
    </div>

    <!-- Header with month/year navigation -->
    <div class="flex items-center justify-between mb-6">
      <button @click="previousMonth" class="p-2 hover:bg-gray-100 rounded">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
      <h2 class="text-lg font-semibold">{{ monthYear }}</h2>
      <button @click="nextMonth" class="p-2 hover:bg-gray-100 rounded">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    </div>

    <!-- Days of week header -->
    <div class="grid grid-cols-7 gap-1 mb-2">
      <div v-for="day in daysOfWeek" :key="day" class="text-center text-sm font-medium text-gray-600 py-2">
        {{ day }}
      </div>
    </div>

    <!-- Calendar grid -->
    <div class="grid grid-cols-7 gap-1 gap-y-2">
      <div v-for="date in calendarDates" :key="`${date.date}-${date.month}`"
        class="aspect-square flex flex-col items-center justify-center text-sm relative cursor-pointer transition-colors"
        :class="getCellClasses(date)" @click="selectDate(date)">
        <span class="font-medium" :class="{ 'text-white': date.isToday }">
          {{ date.date }}
        </span>
        <span v-if="date.shiftCode" class="text-xs font-light mt-0.5" :class="{ 'text-white': date.isToday }">
          {{ date.shiftCode }}
        </span>
      </div>
    </div>
    <!-- Selected date info -->
    <div v-if="selectedDate" class="mt-4 p-3 bg-blue-50 rounded-lg">
      <h3 class="text-sm font-semibold mb-1">Thông tin ngày {{ selectedDate.date }}/{{ selectedDate.month + 1 }}</h3>
      <p class="text-sm text-gray-600">
        <span v-if="selectedDate.shiftCode">Ca làm việc: {{ getShiftName(selectedDate.shiftCode) }}</span>
        <span v-else>Không có ca làm việc</span>
      </p>
      <p class="text-sm text-gray-600 mt-1">
        Chấm công: {{ selectedDate.attendance || 'Chưa chấm' }}
      </p>
    </div>
  </div>
</template>
