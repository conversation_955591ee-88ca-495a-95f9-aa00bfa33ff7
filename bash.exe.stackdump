Stack trace:
Frame         Function      Args
0007FFFFBBD0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFAAD0) msys-2.0.dll+0x2118E
0007FFFFBBD0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBEA8) msys-2.0.dll+0x69BA
0007FFFFBBD0  0002100469F2 (00021028DF99, 0007FFFFBA88, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBBD0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBBD0  00021006A545 (0007FFFFBBE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBEB0  00021006B9A5 (0007FFFFBBE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBDD620000 ntdll.dll
7FFBDC7E0000 KERNEL32.DLL
7FFBDA7B0000 KERNELBASE.dll
7FFBDCB10000 USER32.dll
7FFBDB3C0000 win32u.dll
7FFBDB790000 GDI32.dll
7FFBDB290000 gdi32full.dll
7FFBDB070000 msvcp_win.dll
7FFBDAE90000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBDC500000 advapi32.dll
7FFBDB7C0000 msvcrt.dll
7FFBDC8B0000 sechost.dll
7FFBDB9D0000 RPCRT4.dll
7FFBD9DD0000 CRYPTBASE.DLL
7FFBDABB0000 bcryptPrimitives.dll
7FFBDC7A0000 IMM32.DLL
