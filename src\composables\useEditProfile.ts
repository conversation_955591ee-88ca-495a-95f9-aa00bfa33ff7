import { computed, ref } from 'vue';

import type { User } from '@/interfaces/staff';
import { toast } from 'vue-sonner';
import { useI18n } from 'vue-i18n';
import { useProfile } from '@/composables/useProfile';
import { useUserStore } from '@/stores/user';

export interface ProfileFormData {
  // Personal Information
  fullname_vn: string;
  fullname_en: string;
  birthday: number | null;
  birth_place: string;
  sex: string;
  home_town: string;
  current_address: string;
  religion: string;
  nation: string;
  literacy: string;
  major: string;
  marital_status: string;

  // Contact Information
  // personal_email: string;
  company_email: string;
  phone_number: string;

  // Work Information
  staff_identifi: string;
  position_name: string;
  department_name: string;
  workplace_name: string;
  workplace_address: string;
  start_work_date: number | null;
  vehicle_plate: string;

  // Identity Information
  identification_number: string;
  place_of_issue: string;
  issue_date: number | null;
  personal_tax_code: string;
  social_security_no: string;

  // Banking Information
  account_number: string;
  name_account: string;
  issue_bank: string;
}

export function useEditProfile() {
  const { t } = useI18n();
  const userStore = useUserStore();
  const { updateUser } = useProfile();

  // Form data
  const formData = ref<ProfileFormData>({
    // Personal Information
    fullname_vn: '',
    fullname_en: '',
    birthday: null,
    birth_place: '',
    sex: '',
    home_town: '',
    current_address: '',
    religion: '',
    nation: '',
    literacy: '',
    major: '',
    marital_status: '',

    // Contact Information
    // personal_email: '',
    company_email: '',
    phone_number: '',

    // Work Information
    staff_identifi: '',
    position_name: '',
    department_name: '',
    workplace_name: '',
    workplace_address: '',
    start_work_date: null,
    vehicle_plate: '',

    // Identity Information
    identification_number: '',
    place_of_issue: '',
    issue_date: null,
    personal_tax_code: '',
    social_security_no: '',

    // Banking Information
    account_number: '',
    name_account: '',
    issue_bank: '',
  });

  // Loading states
  const isSubmitting = ref(false);
  const isLoading = ref(false);

  // Options
  const genderOptions = computed(() => [
    { value: 'male', label: t('profile.edit.male') },
    { value: 'female', label: t('profile.edit.female') },
    { value: 'other', label: t('profile.edit.other') },
  ]);

  const educationOptions = computed(() => [
    { value: 'high_school', label: t('profile.edit.high_school') },
    { value: 'bachelor', label: t('profile.edit.bachelor') },
    { value: 'master', label: t('profile.edit.master') },
    { value: 'phd', label: t('profile.edit.phd') },
    { value: 'other', label: t('profile.edit.other') },
  ]);

  const maritalStatusOptions = computed(() => [
    { value: 'single', label: t('profile.edit.single') },
    { value: 'married', label: t('profile.edit.married') },
    { value: 'divorced', label: t('profile.edit.divorced') },
  ]);

  // Function to convert timestamp to MySQL date format
  const formatDateForAPI = (timestamp: number | null): string | null => {
    if (!timestamp) return null;
    const date = new Date(timestamp);
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  };

  // Function to parse date string to timestamp
  const parseDate = (dateStr: string): number | null => {
    if (!dateStr) return null;
    const formats = [
      /^(\d{2})\/(\d{2})\/(\d{4})$/, // DD/MM/YYYY
      /^(\d{2})-(\d{2})-(\d{4})$/, // DD-MM-YYYY
      /^(\d{4})-(\d{2})-(\d{2})$/, // YYYY-MM-DD
    ];

    for (const format of formats) {
      const match = dateStr.match(format);
      if (match) {
        let day, month, year;
        if (format === formats[2]) {
          // YYYY-MM-DD
          [, year, month, day] = match;
        } else {
          // DD/MM/YYYY or DD-MM-YYYY
          [, day, month, year] = match;
        }

        const date = new Date(
          parseInt(year),
          parseInt(month) - 1,
          parseInt(day),
        );
        return date.getTime();
      }
    }

    return null;
  };

  // Load user data into form
  const loadUserData = () => {
    if (userStore.user) {
      const user = userStore.user;

      // Personal Information
      formData.value.fullname_vn = user.fullname_vn || '';
      formData.value.fullname_en = user.fullname_en || '';
      formData.value.birthday = user.birthday
        ? new Date(user.birthday).getTime()
        : null;
      formData.value.birth_place = user.birth_place || '';
      formData.value.sex = user.sex || '';
      formData.value.home_town = user.home_town || '';
      formData.value.current_address = user.current_address || '';
      formData.value.religion = user.religion || '';
      formData.value.nation = user.nation || '';
      formData.value.literacy = user.literacy || '';
      formData.value.major = user.major || '';
      formData.value.marital_status = user.marital_status || '';

      // Contact Information
      // formData.value.personal_email = user.personal_email || '';
      formData.value.company_email = user.company_email || '';
      formData.value.phone_number = user.phone_number || '';

      // Work Information
      formData.value.staff_identifi = user.staff_identifi || '';
      formData.value.position_name = user.position_name || '';
      formData.value.department_name = user.department_name || '';
      formData.value.workplace_name = user.workplace_name || '';
      formData.value.workplace_address = user.workplace_address || '';
      formData.value.start_work_date = user.start_work_date
        ? new Date(user.start_work_date).getTime()
        : null;
      formData.value.vehicle_plate = user.vehicle_plate || '';

      // Identity Information
      formData.value.identification_number = user.identification_number || '';
      formData.value.place_of_issue = user.place_of_issue || '';
      formData.value.issue_date = user.issue_date
        ? new Date(user.issue_date).getTime()
        : null;
      formData.value.personal_tax_code = user.personal_tax_code || '';
      formData.value.social_security_no = user.social_security_no || '';

      // Banking Information
      formData.value.account_number = user.account_number || '';
      formData.value.name_account = user.name_account || '';
      formData.value.issue_bank = user.issue_bank || '';
    }
  };

  // Load scanned data from localStorage
  const loadScannedData = () => {
    try {
      const scannedData = localStorage.getItem('scanned_id_data');
      if (scannedData) {
        const data = JSON.parse(scannedData);

        // Map scanned data to form fields
        if (data.full_name) formData.value.fullname_vn = data.full_name;
        if (data.dob) formData.value.birthday = parseDate(data.dob);
        if (data.add_str) formData.value.current_address = data.add_str;
        if (data.citizen_id)
          formData.value.identification_number = data.citizen_id.toString();
        if (data.director_general_police_department)
          formData.value.place_of_issue =
            data.director_general_police_department;
        if (data.date_issue)
          formData.value.issue_date = parseDate(data.date_issue);
        if (data.sex) formData.value.sex = data.sex.toLowerCase();

        // Clear the stored data after loading
        localStorage.removeItem('scanned_id_data');

        toast.success(t('profile.scan_id_card.data_loaded'));
      }
    } catch (error) {
      console.error('Error loading scanned data:', error);
      toast.error(t('profile.scan_id_card.load_error'));
    }
  };

  // Initialize form data
  const initializeForm = async () => {
    isLoading.value = true;
    try {
      await userStore.getCurrentUser();
      loadUserData();
      loadScannedData();
    } catch (error) {
      console.error('Error initializing form:', error);
      toast.error(`Failed to fetch user profile: ${String(error)}`);
    } finally {
      isLoading.value = false;
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!userStore.user?.id) {
      toast.error(t('profile.edit.user_not_found'));
      return;
    }

    isSubmitting.value = true;

    try {
      // Prepare payload with only non-empty values
      const payload: Partial<User> = {};

      // Personal Information
      if (formData.value.fullname_vn.trim()) {
        payload.fullname_vn = formData.value.fullname_vn.trim();
      }
      if (formData.value.fullname_en.trim()) {
        payload.fullname_en = formData.value.fullname_en.trim();
      }
      if (formData.value.birthday) {
        const dateStr = formatDateForAPI(formData.value.birthday);
        if (dateStr) payload.birthday = dateStr;
      }
      if (formData.value.birth_place.trim()) {
        payload.birth_place = formData.value.birth_place.trim();
      }
      if (formData.value.sex.trim()) {
        payload.sex = formData.value.sex.trim() as any;
      }
      if (formData.value.home_town.trim()) {
        payload.home_town = formData.value.home_town.trim();
      }
      if (formData.value.current_address.trim()) {
        payload.current_address = formData.value.current_address.trim();
      }
      if (formData.value.religion.trim()) {
        payload.religion = formData.value.religion.trim();
      }
      if (formData.value.nation.trim()) {
        payload.nation = formData.value.nation.trim();
      }
      if (formData.value.literacy.trim()) {
        payload.literacy = formData.value.literacy.trim() as any;
      }

      if (formData.value.marital_status.trim()) {
        payload.marital_status = formData.value.marital_status.trim() as any;
      }
      if (formData.value.major.trim()) {
        payload.major = formData.value.major.trim();
      }

      // Contact Information
      // if (formData.value.personal_email.trim()) {
      //   payload.personal_email = formData.value.personal_email.trim();
      // }
      if (formData.value.phone_number.trim()) {
        payload.phone_number = formData.value.phone_number.trim();
      }

      // Work Information
      if (formData.value.position_name.trim()) {
        payload.position_name = formData.value.position_name.trim();
      }
      if (formData.value.department_name.trim()) {
        payload.department_name = formData.value.department_name.trim();
      }
      if (formData.value.workplace_name.trim()) {
        payload.workplace_name = formData.value.workplace_name.trim();
      }
      if (formData.value.workplace_address.trim()) {
        payload.workplace_address = formData.value.workplace_address.trim();
      }
      if (formData.value.start_work_date) {
        const dateStr = formatDateForAPI(formData.value.start_work_date);
        if (dateStr) payload.start_work_date = dateStr;
      }
      if (formData.value.vehicle_plate.trim()) {
        payload.vehicle_plate = formData.value.vehicle_plate.trim();
      }

      // Identity Information
      if (formData.value.identification_number.trim()) {
        payload.identification_number =
          formData.value.identification_number.trim();
      }
      if (formData.value.place_of_issue.trim()) {
        payload.place_of_issue = formData.value.place_of_issue.trim();
      }
      if (formData.value.issue_date) {
        const dateStr = formatDateForAPI(formData.value.issue_date);
        if (dateStr) payload.issue_date = dateStr;
      }
      if (formData.value.personal_tax_code.trim()) {
        payload.personal_tax_code = formData.value.personal_tax_code.trim();
      }
      if (formData.value.social_security_no.trim()) {
        payload.social_security_no = formData.value.social_security_no.trim();
      }

      // Banking Information
      if (formData.value.account_number.trim()) {
        payload.account_number = formData.value.account_number.trim();
      }
      if (formData.value.name_account.trim()) {
        payload.name_account = formData.value.name_account.trim();
      }
      if (formData.value.issue_bank.trim()) {
        payload.issue_bank = formData.value.issue_bank.trim();
      }

      // Call update API
      const result = await updateUser(payload);

      if (result) {
        // Refresh user data
        await userStore.getCurrentUser();
        toast.success(t('profile.edit.save_success'));
      } else {
        toast.error(t('profile.edit.save_error'));
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error(t('profile.edit.save_error'));
    } finally {
      isSubmitting.value = false;
    }
  };

  return {
    // Data
    formData,
    isSubmitting,
    isLoading,

    // Options
    genderOptions,
    educationOptions,
    maritalStatusOptions,

    // Methods
    loadUserData,
    loadScannedData,
    initializeForm,
    handleSubmit,
    formatDateForAPI,
    parseDate,
  };
}
