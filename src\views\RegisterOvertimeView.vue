<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { useAttendanceForm } from '@/composables/useAttendanceForm';
import { useDateFormats } from '@/composables/useDateFormats';
import { useProfile } from '@/composables/useProfile';
import { FormMode } from '@/enums';
import { isEmployee } from '@/helpers/staff-helper';
import { cn } from '@/lib/utils';
import { IonPage } from '@ionic/vue';
import {
  NAlert,
  NCheckbox,
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NTimePicker,
} from 'naive-ui';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const {
  formValue,
  formRef,
  rules,
  alert,
  hasEmployees,
  staffApproveOptions,
  staffOptions,
  workShiftOptions,
  handleSubmit,
} = useAttendanceForm(FormMode.CREATE);
const { user } = useProfile();
const isNonEmployee = computed(() => !isEmployee(user?.value?.role.name || ''));
const { pickerConfigs } = useDateFormats();

// Interface for shift information
interface ShiftInfo {
  shiftCode: string
  shiftName: string
  timeStart: string
  timeEnd: string
  shiftType: 'admin' | 'overtime' | 'holiday'
}

// Sample work schedule data - in real app this would come from API
const workSchedule = ref<Record<string, ShiftInfo>>({
  // September 2025 - sample data
  '2025-09-05': { shiftCode: 'A1', shiftName: 'Ca hành chính A1', timeStart: '08:00', timeEnd: '17:00', shiftType: 'admin' },
  '2025-09-06': { shiftCode: 'A2', shiftName: 'Ca hành chính A2', timeStart: '08:30', timeEnd: '17:30', shiftType: 'admin' },
  '2025-09-07': { shiftCode: 'B1', shiftName: 'Ca hành chính B1', timeStart: '12:00', timeEnd: '21:00', shiftType: 'admin' },
  '2025-09-08': { shiftCode: 'A7', shiftName: 'Ca hành chính A7', timeStart: '08:00', timeEnd: '17:00', shiftType: 'admin' },
  '2025-09-09': { shiftCode: 'BB', shiftName: 'Ca hành chính BB', timeStart: '07:30', timeEnd: '16:30', shiftType: 'admin' },
  '2025-09-10': { shiftCode: 'CM1', shiftName: 'Ca chiều CM1', timeStart: '13:30', timeEnd: '22:30', shiftType: 'admin' },
  '2025-09-11': { shiftCode: 'A4', shiftName: 'Ca hành chính A4', timeStart: '09:00', timeEnd: '18:00', shiftType: 'admin' },
  '2025-09-12': { shiftCode: 'B3', shiftName: 'Ca hành chính B3', timeStart: '14:00', timeEnd: '23:00', shiftType: 'admin' },
  '2025-09-13': { shiftCode: 'A6', shiftName: 'Ca hành chính A6', timeStart: '06:00', timeEnd: '15:00', shiftType: 'admin' },
  '2025-09-14': { shiftCode: 'CMA1', shiftName: 'Ca tăng ca CMA1', timeStart: '17:30', timeEnd: '21:30', shiftType: 'overtime' },
  '2025-09-15': { shiftCode: 'TSHC1', shiftName: 'Ca tăng ca TSHC1', timeStart: '19:30', timeEnd: '23:30', shiftType: 'overtime' },
})

// State for overtime registration mode
const showOvertimeOptions = ref(false)
const selectedShift = ref<ShiftInfo | null>(null)
const overtimeType = ref<'before' | 'after'>('before')

// Check if selected date has work shift
const hasWorkShift = computed(() => {
  if (!formValue.additional_day) return false
  return !!workSchedule.value[formValue.additional_day]
})

// Get shift info for selected date
const shiftInfo = computed(() => {
  if (!formValue.additional_day) return null
  return workSchedule.value[formValue.additional_day] || null
})

// Helper function to convert time string to timestamp
const timeToTimestamp = (timeStr: string): number => {
  const [hours, minutes] = timeStr.split(':').map(Number)
  const date = new Date()
  date.setHours(hours, minutes, 0, 0)
  return date.getTime()
}

// Watch for date changes to check work shift
watch(() => formValue.additional_day, (newDate) => {
  if (newDate && workSchedule.value[newDate]) {
    selectedShift.value = workSchedule.value[newDate]
    showOvertimeOptions.value = true
    // Reset overtime type when date changes
    overtimeType.value = 'before'
  } else {
    selectedShift.value = null
    showOvertimeOptions.value = false
  }
})

// Watch for overtime type changes to auto-adjust time
watch(overtimeType, (newType) => {
  if (!selectedShift.value) return

  if (newType === 'before') {
    // For "before shift", set time_out to shift start time
    formValue.time_out = timeToTimestamp(selectedShift.value.timeStart)
    // Clear time_in to let user input
    formValue.time_in = null
  } else if (newType === 'after') {
    // For "after shift", set time_in to shift end time
    formValue.time_in = timeToTimestamp(selectedShift.value.timeEnd)
    // Clear time_out to let user input
    formValue.time_out = null
  }
})
</script>

<template>
  <ion-page>
    <div class="scroll-container flex h-full flex-col items-center gap-y-4 p-4">
      <n-alert v-if="alert.visible" :title="alert.title" :type="alert.type" closable @close="alert.visible = false"
        bordered class="mb-4 w-full">
        {{ alert.message }}
      </n-alert>

      <n-form ref="formRef" :model="formValue" :rules="rules" class="w-full">
        <n-form-item :label="$t('attendance.register_overtime.additional_day')" path="additional_day" class="w-full"
          required>
          <n-date-picker v-model:formatted-value="formValue.additional_day" type="date"
            :format="pickerConfigs.attendance.date.format" :value-format="pickerConfigs.attendance.date.valueFormat"
            :placeholder="$t('attendance.register_overtime.additional_day_placeholder')
              " clearable class="w-full" />
        </n-form-item>

        <!-- Work Shift Information (shown when date has shift) -->
        <div v-if="selectedShift" class="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h3 class="text-sm font-semibold text-blue-800 mb-2">📅 Thông tin ca làm việc</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            <p><span class="font-medium">Ca:</span> {{ selectedShift.shiftName }} ({{ selectedShift.shiftCode }})</p>
            <p><span class="font-medium">Thời gian:</span> {{ selectedShift.timeStart }} - {{ selectedShift.timeEnd }}
            </p>
            <p><span class="font-medium">Loại ca:</span>
              <span :class="{
                'text-green-600': selectedShift.shiftType === 'admin',
                'text-orange-600': selectedShift.shiftType === 'overtime',
                'text-purple-600': selectedShift.shiftType === 'holiday'
              }">
                {{ selectedShift.shiftType === 'admin' ? 'Hành chính' :
                  selectedShift.shiftType === 'overtime' ? 'Tăng ca' : 'Nghỉ lễ' }}
              </span>
            </p>
          </div>

          <!-- Overtime Type Selection -->
          <div class="mt-3 pt-3 border-t border-blue-200">
            <p class="text-sm font-medium text-blue-800 mb-2">Chọn loại đăng ký:</p>
            <div class="grid grid-cols-2 gap-2">
              <button type="button" class="p-3 text-sm border rounded-lg hover:bg-blue-100 transition-colors"
                :class="overtimeType === 'before' ? 'border-blue-500 bg-blue-100 text-blue-700' : 'border-gray-300'"
                @click="overtimeType = 'before'">
                <div class="font-medium">⏰ Trước ca</div>
                <div class="text-xs text-gray-600">Làm thêm trước {{ selectedShift.timeStart }}</div>
              </button>
              <button type="button" class="p-3 text-sm border rounded-lg hover:bg-blue-100 transition-colors"
                :class="overtimeType === 'after' ? 'border-blue-500 bg-blue-100 text-blue-700' : 'border-gray-300'"
                @click="overtimeType = 'after'">
                <div class="font-medium">🌙 Sau ca</div>
                <div class="text-xs text-gray-600">Làm thêm sau {{ selectedShift.timeEnd }}</div>
              </button>
            </div>
          </div>
        </div>

        <!-- No Shift Warning -->
        <div v-else-if="formValue.additional_day && !selectedShift"
          class="mb-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd"></path>
            </svg>
            <div>
              <p class="text-sm font-medium text-yellow-800">Không có ca làm việc</p>
              <p class="text-xs text-yellow-700">Ngày này bạn không có ca làm việc được phân công. Vui lòng chọn ngày
                khác hoặc liên hệ quản lý.</p>
            </div>
          </div>
        </div>

        <n-checkbox v-if="isNonEmployee" v-model:checked="hasEmployees" size="small"
          :class="cn('w-full', hasEmployees ? 'mb-2' : 'mb-4')">
          {{ t('leaves.new_request.register_for_employee') }}
        </n-checkbox>

        <n-form-item v-if="isNonEmployee && hasEmployees" :label="t('leaves.new_request.staff')" path="staff">
          <n-select filterable multiple v-model:value="formValue.employees" :options="staffOptions"
            :placeholder="t('leaves.new_request.staff_placeholder')" />
        </n-form-item>

        <n-form-item :label="$t('attendance.register_overtime.work_shift')" class="w-full" path="shift_id">
          <n-select v-model:value="formValue.shift_id" filterable :options="workShiftOptions" :placeholder="t('attendance.register_overtime.work_shift_placeholder')
            " />
        </n-form-item>
        <!-- Time Input with Smart Suggestions -->
        <div class="grid grid-cols-2 gap-4">
          <n-form-item path="time_in" class="w-full">
            <template #label>
              <span class="font-semibold text-base">{{ $t('attendance.register_overtime.time_in') }}</span>
              <span v-if="selectedShift && overtimeType === 'after'" class="text-xs text-gray-500 ml-1">
                (Tự động: {{ selectedShift.timeEnd }})
              </span>
            </template>
            <n-time-picker v-model:value="formValue.time_in" :format="pickerConfigs.attendance.time.format"
              :placeholder="selectedShift && overtimeType === 'before' ? 'Nhập giờ bắt đầu trước ca' : $t('attendance.register_overtime.time_in_placeholder')"
              :disabled="!!(selectedShift && overtimeType === 'after')" clearable class="w-full" />
          </n-form-item>

          <n-form-item path="time_out" class="w-full">
            <template #label>
              <span class="font-semibold text-base">{{ $t('attendance.register_overtime.time_out') }}</span>
              <span v-if="selectedShift && overtimeType === 'before'" class="text-xs text-gray-500 ml-1">
                (Tự động: {{ selectedShift.timeStart }})
              </span>
            </template>
            <n-time-picker v-model:value="formValue.time_out" :format="pickerConfigs.attendance.time.format"
              :placeholder="selectedShift && overtimeType === 'after' ? 'Nhập giờ kết thúc sau ca' : $t('attendance.register_overtime.time_out_placeholder')"
              :disabled="!!(selectedShift && overtimeType === 'before')" clearable class="w-full" />
          </n-form-item>
        </div>

        <!-- Time Suggestion Helper -->
        <div v-if="selectedShift" class="mb-4 p-3 bg-gray-50 rounded-lg">
          <p class="text-sm text-gray-600 mb-2">
            <span class="font-medium">💡 Gợi ý:</span>
          </p>
          <div v-if="overtimeType === 'before'" class="text-sm text-gray-700">
            • Nhập <strong>giờ bắt đầu</strong> làm thêm (trước {{ selectedShift.timeStart }})
            <br>
            • Giờ kết thúc sẽ tự động là <strong>{{ selectedShift.timeStart }}</strong> (bắt đầu ca chính)
          </div>
          <div v-else class="text-sm text-gray-700">
            • Giờ bắt đầu sẽ tự động là <strong>{{ selectedShift.timeEnd }}</strong> (kết thúc ca chính)
            <br>
            • Nhập <strong>giờ kết thúc</strong> làm thêm (sau {{ selectedShift.timeEnd }})
          </div>
        </div>

        <n-form-item label-class="font-semibold text-base" :label="$t('attendance.register_overtime.timekeeping_value')"
          path="timekeeping_value" class="w-full">
          <n-input v-model:value="formValue.timekeeping_value" placeholder="" class="w-full" disabled />
        </n-form-item>

        <n-form-item :label="t('attendance.register_overtime.approver')" path="approver_id">
          <n-select v-model:value="formValue.approver_id" filterable :options="staffApproveOptions" :placeholder="t('attendance.register_overtime.approver_placeholder')
            " />
        </n-form-item>
        <n-form-item :label="t('attendance.register_overtime.approver_2')">
          <n-select v-model:value="formValue.approver_id" filterable :options="staffApproveOptions" :placeholder="t('attendance.register_overtime.approver_placeholder')
            " />
        </n-form-item>
        <n-form-item :label="t('attendance.register_overtime.approver_3')">
          <n-select v-model:value="formValue.approver_id" filterable :options="staffApproveOptions" :placeholder="t('attendance.register_overtime.approver_placeholder')
            " />
        </n-form-item>
        <n-form-item :label="t('attendance.register_overtime.approver_4')">
          <n-select v-model:value="formValue.approver_id" filterable :options="staffApproveOptions" :placeholder="t('attendance.register_overtime.approver_placeholder')
            " />
        </n-form-item>


        <n-form-item :label="$t('attendance.register_overtime.reason')" path="reason" class="w-full">
          <n-input v-model:value="formValue.reason" type="textarea" :placeholder="selectedShift && overtimeType ?
            `Nhập lý do đăng ký ${overtimeType === 'before' ? 'trước' : 'sau'} ca ${selectedShift.shiftCode}...` :
            $t('attendance.register_overtime.reason_placeholder')" :autosize="{ minRows: 3, maxRows: 7 }"
            class="!h-[125px] w-full" />
        </n-form-item>

        <!-- Summary Section -->
        <div v-if="selectedShift" class="mb-4 p-4 bg-green-50 rounded-lg border border-green-200">
          <h3 class="text-sm font-semibold text-green-800 mb-2">📋 Tóm tắt đăng ký</h3>
          <div class="space-y-1 text-sm text-green-700">
            <p><span class="font-medium">Ngày:</span> {{ formValue.additional_day }}</p>
            <p><span class="font-medium">Ca chính:</span> {{ selectedShift.shiftName }} ({{ selectedShift.timeStart }} -
              {{
                selectedShift.timeEnd }})</p>
            <p><span class="font-medium">Loại đăng ký:</span>
              <span class="font-semibold">
                {{ overtimeType === 'before' ? '⏰ Trước ca' : '🌙 Sau ca' }}
              </span>
            </p>
            <p v-if="formValue.time_in || formValue.time_out">
              <span class="font-medium">Thời gian làm thêm:</span>
              {{ formValue.time_in ? new Date(formValue.time_in).toLocaleTimeString('vi-VN', {
                hour: '2-digit', minute:
                  '2-digit'
              }) : '--:--' }} -
              {{ formValue.time_out ? new Date(formValue.time_out).toLocaleTimeString('vi-VN', {
                hour: '2-digit', minute:
                  '2-digit'
              }) : '--:--' }}
            </p>
          </div>
        </div>

        <Button size="lg" @click="handleSubmit" class="w-full text-base">
          {{ t('common.submit') }}
        </Button>
      </n-form>
    </div>
  </ion-page>
</template>
