<template>
  <div class="flex justify-between items-center p-2 border rounded-md bg-gray-50">
    <span class="font-medium text-gray-800">{{ shift.code }}</span>
    <span class="text-sm text-gray-600 flex-1 ml-4">{{ shift.description }}</span>
    <span class="text-sm text-gray-500">{{ formatWorkTime(shift.time) }}</span>
  </div>
</template>

<script setup lang="ts">
import type { ShiftType } from '@/interfaces/calendar';

function formatWorkTime(timeRange: string): string {
  if (!timeRange) return ''

  // Tách 2 vế start và end
  const [start, end] = timeRange.split('-').map(t => t.trim())

  // Hàm con để cắt bỏ giây
  const formatHHmm = (time: string) => {
    const [h, m] = time.split(':')
    return `${h.padStart(2, '0')}:${m.padStart(2, '0')}`
  }

  return `${formatHHmm(start)} - ${formatHHmm(end)}`
}


interface Props {
  shift: ShiftType;
}

defineProps<Props>();
</script>
