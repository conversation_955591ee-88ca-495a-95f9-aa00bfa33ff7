import Pusher, { Channel } from 'pusher-js';

const pusher = new Pusher(import.meta.env.VITE_PUSHER_APP_KEY as string, {
  cluster: import.meta.env.VITE_PUSHER_CLUSTER as string,
  forceTLS: true,
});

type NotificationEvent =
  | 'new-notification'
  | 'notification-read'
  | 'notification-deleted'
  | 'notifications-all-read'
  | 'notifications-cleared'
  | 'notification-cleared';

type NotificationCallback = (event: NotificationEvent, data: any) => void;

export const subscribeToNotifications = (
  staffId: string | number,
  callback: NotificationCallback,
): Channel => {
  const channel = pusher.subscribe(`notifications-${staffId}`);

  const events: NotificationEvent[] = [
    'new-notification',
    'notification-read',
    'notification-deleted',
    'notifications-all-read',
    'notifications-cleared',
    'notification-cleared',
  ];

  events.forEach((event) => {
    channel.bind(event, (data: any) => callback(event, data));
  });

  return channel;
};

export const unsubscribeFromNotifications = (channel: Channel): void => {
  channel.unbind_all();
  channel.unsubscribe();
};

export default pusher;
