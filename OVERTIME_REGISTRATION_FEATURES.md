# Tính năng Đăng ký Trước/Sau giờ đã được cập nhật

## C<PERSON><PERSON> tính năng mới đã thêm vào `RegisterOvertimeView.vue`:

### 1. <PERSON><PERSON>m tra Ca làm việc
- **Dữ liệu mẫu**: Thêm `workSchedule` với thông tin chi tiết ca làm việc
- **Tự động kiểm tra**: <PERSON><PERSON> chọn ngà<PERSON>, hệ thống tự động kiểm tra có ca làm việc không
- **Hiển thị thông tin ca**: Hi<PERSON><PERSON> thị mã ca, tên ca, thời gian, loại ca

### 2. Giao diện thông minh
- **Thông tin ca làm việc**: Card hiển thị đầy đủ thông tin ca được phân công
- **C<PERSON>nh báo không có ca**: Hiển thị warning khi ngày được chọn không có ca
- **<PERSON>ân loại ca**: Color-coded theo loại ca (hành chính, tăng ca, nghỉ lễ)

### 3. Lựa chọn Trước/Sau ca
- **2 nút lựa chọn**: "⏰ Trước ca" và "🌙 Sau ca"
- **Giao diện trực quan**: Hiển thị rõ thời gian tham chiếu
- **Tự động cập nhật**: Thay đổi placeholder và logic khi chọn

### 4. Tự động điền thời gian
- **Trước ca**: 
  - Tự động set `time_out` = thời gian bắt đầu ca chính
  - User chỉ cần nhập `time_in` (giờ bắt đầu làm thêm)
  - Disable field `time_out`
  
- **Sau ca**:
  - Tự động set `time_in` = thời gian kết thúc ca chính  
  - User chỉ cần nhập `time_out` (giờ kết thúc làm thêm)
  - Disable field `time_in`

### 5. Gợi ý và hướng dẫn
- **Placeholder thông minh**: Thay đổi theo context
- **Gợi ý thời gian**: Hiển thị trong label
- **Hướng dẫn chi tiết**: Card gợi ý cách nhập thời gian

### 6. Tóm tắt đăng ký
- **Card tóm tắt**: Hiển thị tất cả thông tin đã nhập
- **Thời gian real-time**: Cập nhật ngay khi user nhập
- **Format đẹp**: Hiển thị thời gian theo định dạng Việt Nam

## Dữ liệu mẫu để test:

```javascript
// Các ngày có ca làm việc (tháng 9/2025)
'2025-09-05': Ca A1 (08:00-17:00) - Hành chính
'2025-09-06': Ca A2 (08:30-17:30) - Hành chính  
'2025-09-07': Ca B1 (12:00-21:00) - Hành chính
'2025-09-08': Ca A7 (08:00-17:00) - Hành chính
'2025-09-09': Ca BB (07:30-16:30) - Hành chính
'2025-09-10': Ca CM1 (13:30-22:30) - Hành chính
'2025-09-11': Ca A4 (09:00-18:00) - Hành chính
'2025-09-12': Ca B3 (14:00-23:00) - Hành chính
'2025-09-13': Ca A6 (06:00-15:00) - Hành chính
'2025-09-14': Ca CMA1 (17:30-21:30) - Tăng ca
'2025-09-15': Ca TSHC1 (19:30-23:30) - Tăng ca
```

## Cách sử dụng:

### Bước 1: Chọn ngày
- Chọn ngày trong date picker
- Hệ thống tự động kiểm tra ca làm việc

### Bước 2: Xem thông tin ca (nếu có)
- Thông tin ca sẽ hiển thị trong card xanh
- Kiểm tra thời gian ca để quyết định đăng ký trước/sau

### Bước 3: Chọn loại đăng ký
- Click "⏰ Trước ca" để đăng ký làm thêm trước giờ ca chính
- Click "🌙 Sau ca" để đăng ký làm thêm sau giờ ca chính

### Bước 4: Nhập thời gian
- **Trước ca**: Chỉ nhập giờ bắt đầu, giờ kết thúc tự động
- **Sau ca**: Chỉ nhập giờ kết thúc, giờ bắt đầu tự động

### Bước 5: Kiểm tra tóm tắt
- Xem lại thông tin trong card tóm tắt màu xanh lá
- Đảm bảo thông tin chính xác trước khi submit

## Logic xử lý:

### Kiểm tra ca làm việc:
```javascript
// Watch date changes
watch(() => formValue.additional_day, (newDate) => {
  if (newDate && workSchedule.value[newDate]) {
    selectedShift.value = workSchedule.value[newDate]
    showOvertimeOptions.value = true
  }
})
```

### Tự động điền thời gian:
```javascript
// Watch overtime type changes  
watch(overtimeType, (newType) => {
  if (newType === 'before') {
    formValue.time_out = timeToTimestamp(selectedShift.value.timeStart)
  } else if (newType === 'after') {
    formValue.time_in = timeToTimestamp(selectedShift.value.timeEnd)
  }
})
```

## Tích hợp API (TODO):

1. **Lấy ca làm việc**: Thay `workSchedule` bằng API call
2. **Validation**: Thêm rules validation cho thời gian
3. **Submit**: Bao gồm `overtimeType` trong payload
4. **Error handling**: Xử lý lỗi từ server

## Files đã sửa đổi:

1. `src/views/RegisterOvertimeView.vue` - File chính được cập nhật
2. `OVERTIME_REGISTRATION_FEATURES.md` - Tài liệu này

Tính năng này giúp user dễ dàng đăng ký overtime trước/sau ca làm việc với giao diện trực quan và logic thông minh!
