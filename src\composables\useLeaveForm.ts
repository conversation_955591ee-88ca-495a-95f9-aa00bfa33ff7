import type { FormInst, FormRules } from 'naive-ui';
import { LeaveOfType, RelType } from '@/enums/leave';
import { computed, ref, watch } from 'vue';

import { AttendanceStatus } from '@/enums/attendance';
import { FormMode } from '@/enums';
import { LEAVES } from '@/constants/routes';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { toMySQLTimestamp } from '@/utils/format';
import { toast } from 'vue-sonner';
import { useHistoryStore } from '@/stores/history';
import { useI18n } from 'vue-i18n';
import { useLeaveRequests } from '@/composables/useLeaveRequests';
import { useLocale } from '@/composables/useLocale';
import { useProfile } from '@/composables/useProfile';
import { useRouter } from 'vue-router';
import { useStaff } from '@/composables/useStaff';

/* eslint-disable @typescript-eslint/no-unused-vars */
  interface LeaveFormData {
    subject: string;
    employee_ids: number[];
    start_time: number | null;
    end_time?: number | null;
    reason: string;
    rel_type: RelType;
    type_of_leave?: LeaveOfType | undefined;
    number_of_leaving_day?: number;
    follower_id: number | null;
    approver_id: number | null;
    is_deducted_attendance?: AttendanceStatus | null;
    file: File | null;
  }

  export const useLeaveForm = (
    mode: FormMode = FormMode.CREATE,
    leaveId?: number,
  ) => {
    const { t } = useI18n();
    const router = useRouter();
    const { locale } = useLocale();
    const { user, isLoading: profileLoading } = useProfile();
    const { staffRemainQuery, createMutation, updateMutation, leaveItemQuery } =
      useLeaveRequests();
    const { staffQuery, staffApprovedQuery } = useStaff();
    const historyStore = useHistoryStore();

    const formRef = ref<FormInst | null>(null);
    const hasEmployees = ref<boolean>(false);
    const RelTypeEnum = RelType;

    // Query for update mode
    const { data: leaveData, isLoading: leaveLoading } =
      mode === FormMode.UPDATE && leaveId
        ? leaveItemQuery(leaveId)
        : { data: ref(null), isLoading: ref(false) };

    const createInitialFormData = (): LeaveFormData => ({
      subject: '',
      employee_ids: [],
      start_time: null,
      end_time: null,
      reason: '',
      rel_type: RelType.LEAVE,
      type_of_leave: undefined,
      number_of_leaving_day: 0,
      follower_id: null,
      approver_id: null,
      is_deducted_attendance: null,
      file: null,
    });

    const form = ref<LeaveFormData>(createInitialFormData());

    // Options
    const relTypeOptions = computed(() =>
      Object.entries(RelType)
        .filter(([key]) => isNaN(Number(key)))
        .map(([key, value]) => ({
          label: t(`leaves.rel_type.${key}`),
          value: value,
        })),
    );

    const leaveTypeOptions = computed(() =>
      Object.entries(LeaveOfType)
        .filter(([key]) => isNaN(Number(key)))
        .map(([key, value]) => ({
          label: t(`leaves.leave_type.${key}`),
          value: value,
        })),
    );

    const attendanceStatusOptions = computed(() =>
      Object.entries(AttendanceStatus)
        .filter(([key]) => isNaN(Number(key)))
        .map(([key, value]) => ({
          label: t(`leaves.attendance_status.${key.toLowerCase()}`),
          value: value,
        })),
    );

    const staffList = computed(() => staffQuery.data?.value || []);
    const staffApproveList = computed(() => staffApprovedQuery.data?.value || []);

    const staffManagerOptions = computed(() =>
      staffList.value.map((staff) => ({
        label: staff.staff_fullname_vn,
        value: Number(staff.staff_id),
      })),
    );
    const staffApproveOptions = computed(() =>
      staffApproveList.value.map((staff) => ({
        label: staff.staff_fullname_vn,
        value: Number(staff.staff_id),
      })),
    );

    const staffOptions = computed(() => {
      if (
        staffRemainQuery.data?.value &&
        Array.isArray(staffRemainQuery.data.value)
      ) {
        return staffRemainQuery.data.value.map((staff) => ({
          label: staff.full_name,
          value: staff.staff_id,
        }));
      }
      return [];
    });

    // Computed values for leave calculations
    const currentStaffData = computed(() => {
      if (
        staffRemainQuery.data?.value &&
        Array.isArray(staffRemainQuery.data.value)
      ) {
        return staffRemainQuery.data.value.find(
          (staff) => staff.staff_id === Number(user.value?.id),
        );
      }
      return null;
    });

    const currentLeaveTypeData = computed(() => {
      if (currentStaffData.value && form.value.type_of_leave) {
        return currentStaffData.value.leaves.find(
          (leave) => leave.type_of_leave === form.value.type_of_leave,
        );
      }
      return null;
    });

    const remainDays = computed(() => currentLeaveTypeData.value?.remain ?? 0);
    const totalDaysOff = computed(
      () => currentLeaveTypeData.value?.days_off ?? 0,
    );

    const isSubmitDisabled = computed(() => {
      if (form.value.rel_type === RelTypeEnum.LEAVE) {
        return (
          (form.value?.number_of_leaving_day ?? 0) > remainDays.value ||
          remainDays.value === 0
        );
      }
      return false;
    });

    const isLoading = computed(
      () =>
        profileLoading.value || (mode === FormMode.UPDATE && leaveLoading.value),
    );

    const groupMembers = computed(() => leaveData.value?.group_members || []);
    const isGroupLeave = computed(() => groupMembers.value.length > 0);

    // Form validation rules
    const rules = computed(
      (): FormRules => ({
        subject: {
          required: true,
          message: t('leaves.validation.subject_required'),
          trigger: ['blur', 'change'],
        },
        number_of_leaving_day: {
          required: form.value.rel_type === RelTypeEnum.LEAVE,
          type: 'number',
          min: 0.5,
          message: t('leaves.validation.days_required'),
          trigger: ['blur', 'change'],
        },
      }),
    );

    // Utility functions
    const convertToTimestamp = (mysqlDateTime: string | null): number | null => {
      if (!mysqlDateTime) return null;
      return new Date(mysqlDateTime).getTime();
    };

    const populateForm = () => {
      if (leaveData.value) {
        form.value = {
          subject: leaveData.value.subject || '',
          employee_ids: [],
          start_time: convertToTimestamp(leaveData.value.start_time),
          end_time: convertToTimestamp(leaveData.value.end_time),
          reason: leaveData.value.reason || '',
          rel_type: leaveData.value.rel_type || RelType.LEAVE,
          type_of_leave: leaveData.value.type_of_leave,
          number_of_leaving_day: leaveData.value.number_of_leaving_day || 0,
          follower_id: leaveData.value.follower_id || null,
          approver_id: leaveData.value.approver_id || null,
          is_deducted_attendance: leaveData.value.is_deducted_attendance || null,
          file: null,
        };
      }
    };

    // Watch for data changes in update mode
    if (mode === FormMode.UPDATE) {
      watch(
        leaveData,
        (newData) => {
          if (newData) {
            populateForm();
          }
        },
        { immediate: true },
      );
    }

    // Submit handlers
    const handleCreate = () => {
      formRef.value?.validate((errors) => {
        if (!errors) {
          const { file, ...restForm } = form.value;

          const currentUserId = Number(user.value?.id);
          let employee_ids: number[] = [];

          if (hasEmployees.value) {
            employee_ids = [...form.value.employee_ids];
            if (!employee_ids.includes(currentUserId)) {
              employee_ids.push(currentUserId);
            }
          }

          // Base payload
          const basePayload = {
            subject: form.value.subject,
            start_time: toMySQLTimestamp(form.value.start_time),
            reason: form.value.reason,
            rel_type: form.value.rel_type,
            follower_id: form.value.follower_id,
            approver_id: form.value.approver_id,
            attendance_status: form.value.is_deducted_attendance,
            ...(hasEmployees.value && { employee_ids }),
          };

          let payload;
          if (form.value.rel_type === RelTypeEnum.LEAVE) {
            payload = {
              ...basePayload,
              end_time: toMySQLTimestamp(form.value.end_time ?? null),
              type_of_leave: form.value.type_of_leave,
              number_of_leaving_day: form.value.number_of_leaving_day,
            };
          } else {
            payload = basePayload;
          }

          createMutation.mutate(payload, {
            onSuccess: () => {
              toast.success(
                t('common.toast.success.create', {
                  type: t('common.module_type.leave'),
                }),
              );
              form.value = createInitialFormData();
              router.push(getLocalizedPath(LEAVES, locale.value));
            },
            onError: () => {
              toast.error(
                t('common.toast.error.create', {
                  type: t('common.module_type.leave'),
                }),
              );
            },
          });
        }
      });
    };

    const handleUpdate = () => {
      if (!leaveId) return;

      formRef.value?.validate((errors) => {
        if (!errors) {
          const { file, ...restForm } = form.value;

          const basePayload = {
            subject: form.value.subject,
            start_time: toMySQLTimestamp(form.value.start_time),
            reason: form.value.reason,
            rel_type: form.value.rel_type,
            follower_id: form.value.follower_id,
            approver_id: form.value.approver_id,
            is_deducted_attendance: form.value.is_deducted_attendance,
          };

          let payload;
          if (form.value.rel_type === RelTypeEnum.LEAVE) {
            payload = {
              ...basePayload,
              end_time: toMySQLTimestamp(form.value.end_time ?? null),
              type_of_leave: form.value.type_of_leave,
              number_of_leaving_day: form.value.number_of_leaving_day,
            };
          } else {
            payload = basePayload;
          }

          updateMutation.mutate(
            { id: leaveId, data: payload },
            {
              onSuccess: () => {
                toast.success(
                  t('common.toast.success.update', {
                    type: t('common.module_type.leave'),
                  }),
                );
                router.back();
                historyStore.clearHistory();
                historyStore.addPath(LEAVES);
              },
              onError: () => {
                toast.error(
                  t('common.toast.error.update', {
                    type: t('common.module_type.leave'),
                  }),
                );
              },
            },
          );
        }
      });
    };

    const handleSubmit = mode === FormMode.CREATE ? handleCreate : handleUpdate;

    const handleCancel = () => {
      router.back();
    };

    return {
      // Form data and refs
      form,
      formRef,
      hasEmployees,
      rules,

      // Options
      relTypeOptions,
      leaveTypeOptions,
      attendanceStatusOptions,
      staffOptions,
      staffManagerOptions,
      staffApproveOptions,

      // Computed values
      remainDays,
      totalDaysOff,
      isSubmitDisabled,
      isLoading,
      groupMembers,
      isGroupLeave,
      RelTypeEnum,

      // Methods
      handleSubmit,
      handleCancel,
      populateForm,

      // Mutations
      createMutation,
      updateMutation,

      // User data
      user,
    };
  };
