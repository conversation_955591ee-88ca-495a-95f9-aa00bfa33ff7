<template>
  <div class="notifications">
    <h2>Notifications</h2>
    <div v-if="notifications.length === 0">No notifications</div>
    <ul v-else>
      <li
        v-for="notification in notifications"
        :key="notification.id"
        :class="{ unread: !notification.is_read }"
      >
        <p>{{ notification.description }}</p>
        <small>{{ notification.date_formatted }}</small>
        <button
          v-if="!notification.is_read"
          @click="markAsRead(notification.id)"
        >
          Mark as Read
        </button>
        <button @click="clearNotification(notification.id)">Clear</button>
      </li>
    </ul>
    <button v-if="notifications.length > 0" @click="markAllAsRead">
      Mark All as Read
    </button>
    <button v-if="notifications.length > 0" @click="clearAllNotifications">
      Clear All
    </button>
  </div>
</template>

<script lang="ts" setup>
import {
  subscribeToNotifications,
  unsubscribeFromNotifications,
} from '@/configs/pusher';
import axios from 'axios';
import { onBeforeUnmount, onMounted, ref } from 'vue';

const notifications = ref<any[]>([]);
const staffId = ref<number | null>(null);
let channel: any = null;

const fetchNotifications = async () => {
  try {
    const response = await axios.get('/api/notifications', {
      params: { read_status: 'all', limit: 10 },
    });
    if (response.data.status) {
      notifications.value = response.data.data.notifications;
    }
  } catch (error) {
    console.error('Failed to fetch notifications:', error);
  }
};

const setupPusher = () => {
  if (!staffId.value) return;
  channel = subscribeToNotifications(staffId.value, (event, data) => {
    switch (event) {
      case 'new-notification':
        notifications.value.unshift(data);
        break;
      case 'notification-read': {
        const readNotif = notifications.value.find(
          (n) => n.id === data.notification_id,
        );
        if (readNotif) readNotif.is_read = 1;
        break;
      }
      case 'notification-deleted':
      case 'notification-cleared':
        notifications.value = notifications.value.filter(
          (n) => n.id !== data.notification_id,
        );
        break;
      case 'notifications-all-read':
        notifications.value.forEach((n) => {
          n.is_read = 1;
        });
        break;
      case 'notifications-cleared':
        notifications.value = [];
        break;
    }
  });
};

const markAsRead = async (id: number) => {
  try {
    const response = await axios.put(`/api/notifications/${id}/read`);
    if (response.data.status) {
      const notification = notifications.value.find((n) => n.id === id);
      if (notification) notification.is_read = 1;
    }
  } catch (error) {
    console.error('Failed to mark notification as read:', error);
  }
};

const markAllAsRead = async () => {
  try {
    const response = await axios.put('/api/notifications/read_all');
    if (response.data.status) {
      notifications.value.forEach((n) => {
        n.is_read = 1;
      });
    }
  } catch (error) {
    console.error('Failed to mark all notifications as read:', error);
  }
};

const clearNotification = async (id: number) => {
  try {
    const response = await axios.delete(`/api/notifications/${id}/clear`);
    if (response.data.status) {
      notifications.value = notifications.value.filter((n) => n.id !== id);
    }
  } catch (error) {
    console.error('Failed to clear notification:', error);
  }
};

const clearAllNotifications = async () => {
  try {
    const response = await axios.delete('/api/notifications/clear_all');
    if (response.data.status) {
      notifications.value = [];
    }
  } catch (error) {
    console.error('Failed to clear all notifications:', error);
  }
};

onMounted(async () => {
  staffId.value = 1;
  await fetchNotifications();
  setupPusher();
});

onBeforeUnmount(() => {
  if (channel) {
    unsubscribeFromNotifications(channel);
  }
});
</script>

<style scoped>
.notifications {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}
ul {
  list-style: none;
  padding: 0;
}
li {
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.unread {
  background-color: #f0f8ff;
}
button {
  margin-left: 10px;
  padding: 5px 10px;
  cursor: pointer;
}
</style>
