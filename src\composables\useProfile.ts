import { computed, ref } from 'vue';
import { getProfileUser, updateLanguage, updateProfile } from '@/services/profile.service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query';

import { LanguageValue } from '@/enums/language';
import type { User } from '@/interfaces/staff';
import VueCookies from 'vue-cookies';
import { setLocaleFromLanguage } from '@/utils/set-locale';

export function useProfile() {
  const queryClient = useQueryClient();
  const error = ref<string | null>(null);

  const {
    data: user,
    isLoading,
    refetch: refetchUser,
  } = useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      try {
        const response = await getProfileUser();
        return response.data ?? null;
      } catch (err: any) {
        error.value =
          err.response?.data?.message || 'Failed to fetch user profile';
        return null;
      }
    },
    enabled: !!VueCookies.get('auth-token'), // Only run query if user is authenticated
  });

  const updateUser = async ( id: number, payload: Partial<User>) => {
    try {
      const response = await updateProfile(id, payload);
        return response.data ?? null;
      } catch (err: any) {
        error.value =
          err.response?.data?.message || 'Failed to update user profile';
        return null;
      }
  };

  const languageMutation = useMutation({
    mutationFn: (language: LanguageValue) => updateLanguage(language),
    onSuccess: (response, language) => {
      const currentUser = queryClient.getQueryData<User>(['user']);
      if (currentUser) {
        queryClient.setQueryData(['user'], {
          ...currentUser,
          language,
        });
      }

      setLocaleFromLanguage(language);

      queryClient.invalidateQueries({ queryKey: ['user'] });
    },
    onError: (err: any) => {
      error.value =
        err.response?.data?.message || 'Failed to update language preference';
    },
  });

  const fullName = computed(() => {
    if (!user.value) return '';
    const fullName = user.value.fullname_vn || '';
    return fullName.trim();
  });

  const userInitials = computed(() => {
    if (!user.value) return '';
    const fullName = user.value.fullname_vn?.charAt(0) || '';
    return fullName.toUpperCase();
  });

  const getCurrentUser = async () => {
    error.value = null;
    const result = await refetchUser();
    return !!result.data;
  };

  const modifiedLanguage = async (language: LanguageValue) => {
    error.value = null;

    try {
      const response = await languageMutation.mutateAsync(language);
      return { success: true, message: response.message };
    } catch (err: any) {
      return {
        success: false,
        message:
          err.response?.data?.message || 'Failed to update language preference',
      };
    }
  };

  return {
    user,
    isLoading: computed(
      () => isLoading.value || languageMutation.isPending.value,
    ),
    error,
    fullName,
    userInitials,
    updateUser,
    getCurrentUser,
    modifiedLanguage,
  };
}
