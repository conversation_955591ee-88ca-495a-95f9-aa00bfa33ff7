<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useLocale } from '@/composables/useLocale'
import CalendarHeader from '@/components/ui/calendar/CalendarHeader.vue'
import CalendarGrid from '@/components/ui/calendar/CalendarGrid.vue'
import type { RawShift, Shift, CalendarDay } from '@/interfaces/calendar'
import { shiftDefinitions, scheduleData } from '@/mocks/shift_data'
import { navigateToLocalizedRoute } from '@/utils/localized-navigation'
import { REGISTER_OVERTIME } from '@/constants/routes'
import { Button } from '../ui/button'
import ShiftTable from './ShiftTable.vue'

const { t } = useI18n()
const { locale } = useLocale()

const currentDate = ref<Date>(new Date())
const showDrawer = ref<boolean>(false)
const selectedDay = ref<CalendarDay | null>(null)
const daysOfWeek = computed<string[]>(() => {
  return [
    t('attendance.days.sunday_short'),
    t('attendance.days.monday_short'),
    t('attendance.days.tuesday_short'),
    t('attendance.days.wednesday_short'),
    t('attendance.days.thursday_short'),
    t('attendance.days.friday_short'),
    t('attendance.days.saturday_short')
  ]
})

const calendarDays = computed<CalendarDay[]>(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const firstDay = new Date(year, month, 1)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())
  const days: CalendarDay[] = []
  const today = new Date()

  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)
    const dateString = date.toISOString().split('T')[0]
    const rawShifts: RawShift[] = scheduleData[dateString] || []
    const shifts: Shift[] = rawShifts.map((rawShift: RawShift) => ({
      ...rawShift,
      ...shiftDefinitions[rawShift.code]
    }))
    days.push({
      date: new Date(date),
      day: date.getDate(),
      is_current_month: date.getMonth() === month,
      is_today: date.toDateString() === today.toDateString(),
      has_shift: shifts.length > 0,
      shifts: shifts
    })
  }
  return days
})

const previousMonth = (): void => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
}

const nextMonth = (): void => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
}

const selectDay = (dayData: CalendarDay): void => {
  if (dayData.is_current_month) {
    selectedDay.value = dayData
    showDrawer.value = true
  }
}
const getShiftColor = (shiftCode: string): string => {
  const shiftType = shiftDefinitions[shiftCode]?.type
  switch (shiftType) {
    case 'Day': return 'bg-emerald-100 text-emerald-500 border border-emerald-500'
    case 'Night': return 'bg-blue-100 text-blue-500 border border-blue-500'
    case 'CM': return 'bg-purple-100 text-purple-500 border border-purple-500'
    case 'Admin': return 'bg-orange-100 text-orange-500 border border-orange-500'
    case 'Maternity': return 'bg-pink-100 text-pink-500 border border-pink-500'
    default: return 'bg-gray-500'
  }
}
</script>

<template>
  <h1 class="text-lg w-full mt-2 px-1 font-semibold">
    {{ t('attendance.work_schedule.title') }}
  </h1>
  <div class="py-1 px-1 w-full">
    <div class="bg-white rounded-lg shadow-sm p-3 mb-4">
      <CalendarHeader :current-date="currentDate" @previous-month="previousMonth" @next-month="nextMonth" />
    </div>
    <div class="bg-white rounded-lg shadow-sm p-4 xs:p-1.5">
      <CalendarGrid :days-of-week="daysOfWeek" :calendar-days="calendarDays" :get-shift-color="getShiftColor"
        @select-day="selectDay" />
    </div>
    <ShiftTable />
    <Button size="lg" class="w-full mt-4" @click="() => navigateToLocalizedRoute(REGISTER_OVERTIME, locale)">
      {{ t('attendance.register_overtime.title') }}
    </Button>
  </div>
</template>
