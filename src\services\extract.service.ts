import type { CitizenInfo } from "@/interfaces/staff";

const apiScan = import.meta.env.VITE_SCAN_CITIZENT_URL;
export const extractIdentifi = async (
  frontFile: File,
  backFile: File
): Promise<CitizenInfo> => {
  const formData = new FormData();
  formData.append("front", frontFile);
  formData.append("back", backFile);

  const response = await fetch(`${apiScan}/citizen/scan`, {
    method: "POST",
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data: CitizenInfo = await response.json();

  return data;
};
